#!/usr/bin/env python3
"""
CUDA Setup Verification Script

This script checks the CUDA installation and GPU availability for YOLO-NAS training.
It provides detailed information about the hardware setup and recommendations.

Usage:
    python scripts/utils/check_cuda.py
"""

import torch
import sys
import platform
from pathlib import Path

def print_separator(title=""):
    """Print a formatted separator line."""
    if title:
        print(f"\n{'='*20} {title} {'='*20}")
    else:
        print("="*60)

def check_python_version():
    """Check Python version compatibility."""
    print_separator("Python Environment")
    python_version = sys.version_info
    print(f"Python Version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major == 3 and python_version.minor >= 8:
        print("✅ Python version is compatible")
    else:
        print("❌ Python 3.8+ is required for YOLO-NAS")
        return False
    
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.machine()}")
    return True

def check_pytorch():
    """Check PyTorch installation and version."""
    print_separator("PyTorch Installation")
    
    try:
        print(f"PyTorch Version: {torch.__version__}")
        print(f"PyTorch CUDA Version: {torch.version.cuda}")
        
        # Check if PyTorch was compiled with CUDA support
        if torch.cuda.is_available():
            print("✅ PyTorch was compiled with CUDA support")
        else:
            print("❌ PyTorch was not compiled with CUDA support")
            return False
            
    except Exception as e:
        print(f"❌ Error checking PyTorch: {e}")
        return False
    
    return True

def check_cuda_availability():
    """Check CUDA availability and GPU information."""
    print_separator("CUDA Availability")
    
    if not torch.cuda.is_available():
        print("❌ CUDA is not available")
        print("\nPossible reasons:")
        print("1. No NVIDIA GPU installed")
        print("2. NVIDIA drivers not installed")
        print("3. CUDA toolkit not installed")
        print("4. PyTorch installed without CUDA support")
        return False
    
    print("✅ CUDA is available")
    
    # GPU Information
    gpu_count = torch.cuda.device_count()
    print(f"Number of GPUs: {gpu_count}")
    
    for i in range(gpu_count):
        gpu_name = torch.cuda.get_device_name(i)
        gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)  # GB
        print(f"GPU {i}: {gpu_name}")
        print(f"  Memory: {gpu_memory:.1f} GB")
        
        # Check memory usage
        torch.cuda.set_device(i)
        memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
        memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
        print(f"  Memory Allocated: {memory_allocated:.2f} GB")
        print(f"  Memory Reserved: {memory_reserved:.2f} GB")
    
    return True

def check_cuda_operations():
    """Test basic CUDA operations."""
    print_separator("CUDA Operations Test")
    
    if not torch.cuda.is_available():
        print("❌ Skipping CUDA operations test (CUDA not available)")
        return False
    
    try:
        # Test tensor creation on GPU
        device = torch.cuda.current_device()
        print(f"Current CUDA device: {device}")
        
        # Create tensors on GPU
        x = torch.randn(1000, 1000, device='cuda')
        y = torch.randn(1000, 1000, device='cuda')
        
        # Perform matrix multiplication
        z = torch.matmul(x, y)
        
        # Move back to CPU
        z_cpu = z.cpu()
        
        print("✅ Basic CUDA operations successful")
        print(f"Test tensor shape: {z.shape}")
        print(f"Test tensor device: {z.device}")
        
        # Clear GPU memory
        del x, y, z, z_cpu
        torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        print(f"❌ CUDA operations test failed: {e}")
        return False

def check_memory_requirements():
    """Check if GPU memory meets training requirements."""
    print_separator("Memory Requirements")
    
    if not torch.cuda.is_available():
        print("❌ Cannot check GPU memory (CUDA not available)")
        return False
    
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # GB
    
    print(f"Available GPU Memory: {gpu_memory:.1f} GB")
    
    # Memory requirements for different batch sizes
    requirements = {
        "Minimum (batch_size=4)": 4.0,
        "Recommended (batch_size=8)": 6.0,
        "Optimal (batch_size=16)": 8.0,
        "Large (batch_size=32)": 12.0
    }
    
    print("\nMemory Requirements:")
    for config, required_memory in requirements.items():
        if gpu_memory >= required_memory:
            print(f"✅ {config}: {required_memory} GB")
        else:
            print(f"❌ {config}: {required_memory} GB (insufficient)")
    
    # Recommendations
    print("\nRecommendations:")
    if gpu_memory >= 8.0:
        print("🚀 Your GPU has sufficient memory for optimal training")
        print("   Recommended batch_size: 16-32")
    elif gpu_memory >= 6.0:
        print("👍 Your GPU has good memory for training")
        print("   Recommended batch_size: 8-16")
    elif gpu_memory >= 4.0:
        print("⚠️  Your GPU has minimal memory for training")
        print("   Recommended batch_size: 4-8")
        print("   Consider using gradient accumulation")
    else:
        print("❌ Your GPU has insufficient memory for training")
        print("   Consider using CPU training (much slower)")
        return False
    
    return True

def check_dependencies():
    """Check if required dependencies are installed."""
    print_separator("Dependencies Check")
    
    required_packages = [
        'super_gradients',
        'roboflow',
        'supervision',
        'opencv-python',
        'pillow',
        'numpy',
        'pandas',
        'matplotlib',
        'seaborn',
        'tensorboard',
        'tqdm',
        'pyyaml',
        'albumentations'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'opencv-python':
                import cv2
                print(f"✅ {package} (cv2): {cv2.__version__}")
            elif package == 'pillow':
                from PIL import Image
                print(f"✅ {package} (PIL): {Image.__version__}")
            elif package == 'super_gradients':
                import super_gradients
                print(f"✅ {package}: {super_gradients.__version__}")
            else:
                module = __import__(package.replace('-', '_'))
                version = getattr(module, '__version__', 'unknown')
                print(f"✅ {package}: {version}")
        except ImportError:
            print(f"❌ {package}: Not installed")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install missing packages with:")
        print(f"uv pip install {' '.join(missing_packages)}")
        return False
    else:
        print("\n✅ All required dependencies are installed")
        return True

def main():
    """Main function to run all checks."""
    print("YOLO-NAS CUDA Setup Verification")
    print("="*60)
    
    checks = [
        ("Python Version", check_python_version),
        ("PyTorch Installation", check_pytorch),
        ("CUDA Availability", check_cuda_availability),
        ("CUDA Operations", check_cuda_operations),
        ("Memory Requirements", check_memory_requirements),
        ("Dependencies", check_dependencies)
    ]
    
    results = {}
    
    for check_name, check_function in checks:
        try:
            results[check_name] = check_function()
        except Exception as e:
            print(f"❌ Error in {check_name}: {e}")
            results[check_name] = False
    
    # Summary
    print_separator("Summary")
    
    passed_checks = sum(results.values())
    total_checks = len(results)
    
    print(f"Checks passed: {passed_checks}/{total_checks}")
    
    for check_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{check_name}: {status}")
    
    if passed_checks == total_checks:
        print("\n🎉 All checks passed! Your system is ready for YOLO-NAS training.")
    else:
        print(f"\n⚠️  {total_checks - passed_checks} check(s) failed. Please address the issues above.")
    
    print_separator()

if __name__ == "__main__":
    main()
