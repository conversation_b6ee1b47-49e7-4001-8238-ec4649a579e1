# YOLO-NAS Training Configuration for Barcode Detection
# This configuration file contains all the parameters needed for training
# YOLO-NAS models on the barcode detection dataset

# Model Configuration
model:
  name: "yolo_nas_s" # Options: yolo_nas_s, yolo_nas_m, yolo_nas_l
  pretrained_weights: "coco" # Use COCO pre-trained weights for transfer learning

# Dataset Configuration (COCO Format)
dataset:
  data_dir: "Barcodes.v5i.coco"
  train_images_dir: "Barcodes.v5i.coco/train"
  train_annotations: "Barcodes.v5i.coco/train/_annotations.coco.json"
  val_images_dir: "Barcodes.v5i.coco/valid"
  val_annotations: "Barcodes.v5i.coco/valid/_annotations.coco.json"
  test_images_dir: "Barcodes.v5i.coco/test"
  test_annotations: "Barcodes.v5i.coco/test/_annotations.coco.json"

  # Class names for barcode detection (from COCO dataset)
  classes:
    - "Barcodes"
    - "Barcode"
    - "QR Code"

  # Data augmentation settings
  augmentation:
    enabled: true
    mosaic_prob: 0.5
    mixup_prob: 0.15
    hsv_prob: 0.5
    flip_prob: 0.5
    rotation_prob: 0.2
    scale_range: [0.8, 1.2]

# Training Configuration
training:
  epochs: 100
  batch_size: 16
  learning_rate: 0.001
  num_workers: 4
  mixed_precision: true

  # Early stopping
  early_stopping:
    enabled: true
    patience: 15
    min_delta: 0.001

  # Learning rate scheduling
  lr_scheduler:
    type: "cosine"
    warmup_epochs: 3
    warmup_lr: 0.0001
    final_lr_ratio: 0.1

  # Optimizer settings
  optimizer:
    type: "AdamW"
    weight_decay: 0.0001
    betas: [0.9, 0.999]

  # Model averaging
  ema:
    enabled: true
    decay: 0.9999
    decay_type: "threshold"

# Validation Configuration
validation:
  interval: 1 # Validate every N epochs
  save_best_model: true
  metric_to_watch: "mAP@0.50"

  # Detection thresholds
  confidence_threshold: 0.25
  nms_threshold: 0.45
  max_detections: 300

# Logging Configuration
logging:
  tensorboard: true
  wandb: false # Set to true if using Weights & Biases
  log_interval: 10 # Log every N batches
  save_checkpoint_interval: 10 # Save checkpoint every N epochs

  # Metrics to track
  metrics:
    - "mAP@0.50"
    - "mAP@0.50:0.95"
    - "Precision"
    - "Recall"
    - "F1"

# Hardware Configuration
hardware:
  device: "auto" # auto, cpu, cuda, cuda:0, etc.
  multi_gpu: false
  sync_bn: false

# Inference Configuration (for evaluation)
inference:
  confidence_threshold: 0.5
  nms_threshold: 0.5
  max_detections: 100
  input_size: [640, 640] # [height, width]

# Export Configuration
export:
  formats: ["onnx", "tensorrt"] # Export formats after training
  optimize: true
  simplify: true
