[project]
name = "yolo-nas-barcode-detection"
version = "0.1.0"
description = "YOLO-NAS training project for barcode detection benchmarking"
authors = [
    {name = "Barcode Detection Team"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
dependencies = [
    "super-gradients==3.1.1",
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "torchaudio>=2.0.0",
    "roboflow>=1.1.0",
    "supervision>=0.16.0",
    "tensorboard>=2.13.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "opencv-python>=4.8.0",
    "pillow>=10.0.0",
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "pyyaml>=6.0",
    "tqdm>=4.65.0",
    "scikit-learn>=1.3.0",
    "albumentations>=1.3.0",
    "pycocotools>=2.0.6",
    "jupyter>=1.0.0",
    "ipykernel>=6.25.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "black>=23.7.0",
    "flake8>=6.0.0",
    "isort>=5.12.0",
    "pre-commit>=3.3.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["scripts", "configs"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
