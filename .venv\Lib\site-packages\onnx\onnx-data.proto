//
// WARNING: This file is automatically generated!  Please edit onnx.in.proto.
//


// SPDX-License-Identifier: Apache-2.0


syntax = "proto2";

package onnx;
import "onnx/onnx-ml.proto";

// This file contains the proto definitions for MapProto and
// SequenceProto. These protos are used to represent the data structures
// of maps and sequence for use in test data or ModelProto.

// Sequences
//
// Defines a dense, ordered, collection of elements that are of homogeneous types.
// Sequences can be made out of tensors, maps, or sequences.
//
// If a sequence is made out of tensors, the tensors must have the same element
// type (i.e. int32). In some cases, the tensors in a sequence can have different
// shapes.  Whether the tensors can have different shapes or not depends on the
// type/shape associated with the corresponding "ValueInfo". For example,
// "Sequence<Tensor<float, [M,N]>" means that all tensors have same shape. However,
// "Sequence<Tensor<float, [omitted,omitted]>" means they can have different
// shapes (all of rank 2), where "omitted" means the corresponding dimension has
// no symbolic/constant value. Finally, "Sequence<Tensor<float, omitted>>" means
// that the different tensors can have different ranks, when the "shape" itself
// is omitted from the tensor-type. For a more complete description, refer to
// https://github.com/onnx/onnx/blob/main/docs/IR.md#static-tensor-shapes.
//
message SequenceProto {

  optional string name = 1;

  enum DataType {
    UNDEFINED = 0;
    TENSOR = 1;
    SPARSE_TENSOR = 2;
    SEQUENCE = 3;
    MAP = 4;
    OPTIONAL = 5;
  }

  // The data type of the element.
  // This field MUST have a valid SequenceProto.DataType value
  optional int32 elem_type = 2;

  // For TensorProto values.
  // When this field is present, the elem_type field MUST be TENSOR.
  repeated TensorProto tensor_values = 3;

  // For SparseTensorProto values.
  // When this field is present, the elem_type field MUST be SPARSE_TENSOR.
  repeated SparseTensorProto sparse_tensor_values = 4;

  // For SequenceProto values, allowing sequences to be of themselves.
  // When this field is present, the elem_type field MUST be SEQUENCE.
  repeated SequenceProto sequence_values = 5;

  // For MapProto values.
  // When this field is present, the elem_type field MUST be MAP.
  repeated MapProto map_values = 6;

  // For OptionalProto values.
  // When this field is present, the elem_type field MUST be Optional.
  repeated OptionalProto optional_values = 7;

}


// Maps
//
// Specifies an associative table, defined by keys and values.
// MapProto is formed with a repeated field of keys (of type INT8, INT16, INT32,
// INT64, UINT8, UINT16, UINT32, UINT64, or STRING) and values (of type TENSOR,
// SPARSE_TENSOR, SEQUENCE, or MAP). Key types and value types have to remain
// the same throughout the instantiation of the MapProto.
//
message MapProto {

  optional string name = 1;

  // All MapProto data types must have the same length of keys and values.

  // The data type of the key.
  // This field MUST have a valid TensorProto.DataType value of
  // INT8, INT16, INT32, INT64, UINT8, UINT16, UINT32, UINT64, or STRING
  optional int32 key_type = 2;

  // Every element of keys has to be one of the following data types
  // INT8, INT16, INT32, INT64, UINT8, UINT16, UINT32, UINT64, or STRING.
  // The integer cases are represented by the repeated int64 field keys below.
  repeated int64 keys = 3;

  // If keys are strings, they are represented by the repeated bytes field
  // string_keys below.
  repeated bytes string_keys = 4;

  // MapProto values are represented in a SequenceProto of the same length as the
  // repeated keys field and have to be one of the following data types
  // TENSOR, SPARSE_TENSOR, MAP, SEQUENCE.
  optional SequenceProto values = 5;
}

// Optional
//
//
message OptionalProto {

  optional string name = 1;

  enum DataType {
    UNDEFINED = 0;
    TENSOR = 1;
    SPARSE_TENSOR = 2;
    SEQUENCE = 3;
    MAP = 4;
    OPTIONAL = 5;
  }

  // The data type of the element, identifies if the OptionalProto value
  // is Tensor, Sparse Tensor, Sequence, Map, or Optional.
  // The type of the optional value MUST match the elem_type specified.
  // This field MUST have a valid OptionalProto.DataType value.
  optional int32 elem_type = 2;

  // For TensorProto value.
  // When this field is present, the elem_type field MUST be TENSOR.
  optional TensorProto tensor_value = 3;

  // For SparseTensorProto value.
  // When this field is present, the elem_type field MUST be SPARSE_TENSOR.
  optional SparseTensorProto sparse_tensor_value = 4;

  // For SequenceProto value.
  // When this field is present, the elem_type field MUST be SEQUENCE.
  optional SequenceProto sequence_value = 5;

  // For MapProto value.
  // When this field is present, the elem_type field MUST be MAP.
  optional MapProto map_value = 6;

  // For OptionalProto value, allowing optional to be of itself (completeness)
  // When this field is present, the elem_type field MUST be OPTIONAL.
  optional OptionalProto optional_value = 7;

}

// For using protobuf-lite
option optimize_for = LITE_RUNTIME;

