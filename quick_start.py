#!/usr/bin/env python3
"""
Quick Start Script for YOLO-NAS Barcode Detection

This script provides a streamlined setup and training process for YOLO-NAS
with your COCO format dataset. No conversion needed!

Usage:
    python quick_start.py                    # Install dependencies and start training
    python quick_start.py --install-only     # Only install dependencies
    python quick_start.py --train-only       # Only start training (skip installation)
    python quick_start.py --check-only       # Only check environment
"""

import argparse
import subprocess
import sys
import os
import json
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_command(command, check=True):
    """Run a command and return success status."""
    try:
        result = subprocess.run(command, check=check, capture_output=True, text=True, shell=True)
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr


def check_dataset():
    """Check if the COCO dataset is available and valid."""
    logger.info("Checking dataset...")
    
    dataset_path = Path("Barcodes.v5i.coco")
    if not dataset_path.exists():
        logger.error("❌ Dataset not found: Barcodes.v5i.coco")
        logger.info("Please ensure your COCO dataset is in the 'Barcodes.v5i.coco' directory")
        return False
    
    # Check required files
    required_files = [
        "train/_annotations.coco.json",
        "valid/_annotations.coco.json", 
        "test/_annotations.coco.json"
    ]
    
    for file_path in required_files:
        full_path = dataset_path / file_path
        if not full_path.exists():
            logger.error(f"❌ Missing annotation file: {full_path}")
            return False
    
    # Check classes in dataset
    try:
        with open(dataset_path / "train/_annotations.coco.json", 'r') as f:
            coco_data = json.load(f)
        
        categories = coco_data.get('categories', [])
        class_names = [cat['name'] for cat in categories]
        
        logger.info(f"✅ Dataset found with {len(categories)} classes: {class_names}")
        
        # Count images and annotations
        images_count = len(coco_data.get('images', []))
        annotations_count = len(coco_data.get('annotations', []))
        
        logger.info(f"✅ Training set: {images_count} images, {annotations_count} annotations")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error reading dataset: {e}")
        return False


def install_dependencies():
    """Install required dependencies."""
    logger.info("Installing dependencies...")
    
    # Check if we're in a virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    
    if not in_venv:
        logger.warning("⚠️  Not in a virtual environment. Consider activating .venv first:")
        logger.warning("   .venv\\Scripts\\activate  (Windows)")
        logger.warning("   source .venv/bin/activate  (Linux/Mac)")
    
    # Essential packages for YOLO-NAS
    packages = [
        "torch>=2.0.0",
        "torchvision>=0.15.0", 
        "super-gradients",
        "opencv-python",
        "pillow",
        "numpy",
        "matplotlib",
        "pyyaml",
        "tqdm",
        "tensorboard"
    ]
    
    failed_packages = []
    
    for package in packages:
        logger.info(f"Installing {package}...")
        success, stdout, stderr = run_command(f"pip install {package}", check=False)
        
        if success:
            logger.info(f"✅ {package} installed successfully")
        else:
            logger.error(f"❌ Failed to install {package}")
            failed_packages.append(package)
    
    if failed_packages:
        logger.error(f"Failed to install: {', '.join(failed_packages)}")
        logger.info("Try installing manually:")
        for pkg in failed_packages:
            logger.info(f"  pip install {pkg}")
        return False
    
    logger.info("✅ All dependencies installed successfully")
    return True


def check_environment():
    """Check if the environment is ready for training."""
    logger.info("Checking environment...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major == 3 and python_version.minor >= 8:
        logger.info(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        logger.error("❌ Python 3.8+ required")
        return False
    
    # Check key imports
    try:
        import torch
        logger.info(f"✅ PyTorch {torch.__version__}")
        
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            logger.info(f"✅ CUDA available: {gpu_name}")
        else:
            logger.warning("⚠️  CUDA not available - training will use CPU (slower)")
        
    except ImportError:
        logger.error("❌ PyTorch not installed")
        return False
    
    try:
        import super_gradients
        logger.info(f"✅ Super-gradients {super_gradients.__version__}")
    except ImportError:
        logger.error("❌ Super-gradients not installed")
        return False
    
    # Check other essential packages
    essential_packages = ['cv2', 'PIL', 'numpy', 'yaml', 'matplotlib']
    for pkg in essential_packages:
        try:
            __import__(pkg)
            logger.info(f"✅ {pkg} available")
        except ImportError:
            logger.error(f"❌ {pkg} not available")
            return False
    
    logger.info("✅ Environment check passed")
    return True


def start_training():
    """Start YOLO-NAS training."""
    logger.info("Starting YOLO-NAS training...")
    
    # Check if training script exists
    train_script = Path("scripts/train.py")
    if not train_script.exists():
        logger.error("❌ Training script not found: scripts/train.py")
        return False
    
    # Check if config exists
    config_file = Path("configs/training/yolo_nas_default.yaml")
    if not config_file.exists():
        logger.error("❌ Configuration file not found: configs/training/yolo_nas_default.yaml")
        return False
    
    # Start training
    logger.info("🚀 Starting training with YOLO-NAS...")
    logger.info("This may take several hours depending on your hardware.")
    logger.info("You can monitor progress with TensorBoard:")
    logger.info("  tensorboard --logdir logs/")
    
    command = f"python scripts/train.py --config configs/training/yolo_nas_default.yaml"
    
    try:
        # Run training in real-time (no capture)
        result = subprocess.run(command, shell=True)
        
        if result.returncode == 0:
            logger.info("✅ Training completed successfully!")
            logger.info("Check the 'models/best/' directory for your trained model")
            return True
        else:
            logger.error("❌ Training failed")
            return False
            
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        return False
    except Exception as e:
        logger.error(f"❌ Training failed with error: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description='Quick start YOLO-NAS barcode detection training')
    parser.add_argument('--install-only', action='store_true',
                        help='Only install dependencies')
    parser.add_argument('--train-only', action='store_true', 
                        help='Only start training (skip installation)')
    parser.add_argument('--check-only', action='store_true',
                        help='Only check environment')
    
    args = parser.parse_args()
    
    logger.info("🚀 YOLO-NAS Barcode Detection Quick Start")
    logger.info("=" * 60)
    
    # Check dataset first
    if not check_dataset():
        logger.error("Dataset check failed. Please fix dataset issues before proceeding.")
        sys.exit(1)
    
    if args.check_only:
        success = check_environment()
        sys.exit(0 if success else 1)
    
    if args.train_only:
        if not check_environment():
            logger.error("Environment check failed. Please install dependencies first.")
            sys.exit(1)
        
        success = start_training()
        sys.exit(0 if success else 1)
    
    if args.install_only:
        success = install_dependencies()
        sys.exit(0 if success else 1)
    
    # Full workflow: install dependencies and start training
    logger.info("Step 1: Installing dependencies...")
    if not install_dependencies():
        logger.error("Dependency installation failed")
        sys.exit(1)
    
    logger.info("\\nStep 2: Checking environment...")
    if not check_environment():
        logger.error("Environment check failed")
        sys.exit(1)
    
    logger.info("\\nStep 3: Starting training...")
    if not start_training():
        logger.error("Training failed")
        sys.exit(1)
    
    logger.info("\\n🎉 Setup and training completed successfully!")
    logger.info("\\nNext steps:")
    logger.info("1. Check training results in 'models/best/'")
    logger.info("2. Evaluate your model: python scripts/evaluate.py")
    logger.info("3. Run inference: python scripts/inference.py")


if __name__ == "__main__":
    main()
