# This is not "true" dataset params, one cannot use it to instantiate dataloaders
# But it contains skeleton definitions for COCO2017 dataset and exists to avoid
# duplication of those parameters in other dataset params

num_joints: 17

# OKs sigma values take from https://github.com/cocodataset/cocoapi/blob/master/PythonAPI/pycocotools/cocoeval.py#L523
oks_sigmas: [0.026, 0.025, 0.025, 0.035, 0.035, 0.079, 0.079, 0.072, 0.072, 0.062, 0.062, 0.107, 0.107, 0.087, 0.087, 0.089, 0.089]

flip_indexes: [ 0, 2, 1, 4, 3, 6, 5, 8, 7, 10, 9, 12, 11, 14, 13, 16, 15,]

edge_links:
  - [0, 1]
  - [0, 2]
  - [1, 2]
  - [1, 3]
  - [2, 4]
  - [3, 5]
  - [4, 6]
  - [5, 6]
  - [5, 7]
  - [5, 11]
  - [6, 8]
  - [6, 12]
  - [7, 9]
  - [8, 10]
  - [11, 12]
  - [11, 13]
  - [12, 14]
  - [13, 15]
  - [14, 16]

edge_colors:
 - [214, 39, 40]  # Nose -> LeftEye
 - [148, 103, 189]  # Nose -> RightEye
 - [44, 160, 44]  # LeftEye -> RightEye
 - [140, 86, 75]  # LeftEye -> LeftEar
 - [227, 119, 194]  # RightEye -> RightEar
 - [127, 127, 127]  # LeftEar -> LeftShoulder
 - [188, 189, 34]  # RightEar -> RightShoulder
 - [127, 127, 127]  # Shoulders
 - [188, 189, 34]  # LeftShoulder -> LeftElbow
 - [140, 86, 75]  # LeftTorso
 - [23, 190, 207]  # RightShoulder -> RightElbow
 - [227, 119, 194]  # RightTorso
 - [31, 119, 180]  # LeftElbow -> LeftArm
 - [255, 127, 14]  # RightElbow -> RightArm
 - [148, 103, 189]  # Waist
 - [255, 127, 14]  # Left Hip -> Left Knee
 - [214, 39, 40]  # Right Hip -> Right Knee
 - [31, 119, 180]  # Left Knee -> Left Ankle
 - [44, 160, 44]  # Right Knee -> Right Ankle


keypoint_colors:
  - [148, 103, 189]
  - [31, 119, 180]
  - [148, 103, 189]
  - [31, 119, 180]
  - [148, 103, 189]
  - [31, 119, 180]
  - [148, 103, 189]
  - [31, 119, 180]
  - [148, 103, 189]
  - [31, 119, 180]
  - [148, 103, 189]
  - [31, 119, 180]
  - [148, 103, 189]
  - [31, 119, 180]
  - [148, 103, 189]
  - [31, 119, 180]
  - [148, 103, 189]
