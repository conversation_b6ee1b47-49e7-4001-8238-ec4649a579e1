anchors:
  _target_: super_gradients.training.utils.detection_utils.Anchors
  anchors_list: [[10, 13, 16, 30, 33, 23],[30, 61, 62, 45, 59, 119],[116, 90, 156, 198, 373, 326]]
  strides: [8, 16, 32]

num_classes: 80  # Number of classes to predict
depth_mult_factor: 1.0  # depth multiplier for the entire model, overridden for predefined YoloV5S, YoloV5M, YoloV5L
width_mult_factor: 1.0  # width multiplier for the entire model, overridden for predefined YoloV5S, YoloV5M, YoloV5L
channels_in: 3  # Number of channels in the input image
skip_connections_list: [[12, [6]], [16, [4]], [19, [14]], [22, [10]], [24, [17, 20]]]
# A list defining skip connections. format is [target: [source1, source2, ...]]. Each item defines a skip
# connection from all sources to the target according to the layers index (count starts from the backbone)
backbone_connection_channels: [1024, 512, 256]  # width of backbone channels that are concatenated with the head
scaled_backbone_width: True  # True if width_mult_factor is applied to the backbone
# (is the case with the default backbones)
# which means that backbone_connection_channels should be used with a width_mult_factor
# False if backbone_connection_channels should be used as is
fuse_conv_and_bn: False  # Fuse sequential Conv + B.N layers into a single one
add_nms: False  # Add the NMS module to the computational graph
nms_conf: 0.25  # When add_nms is True during NMS predictions with confidence lower than this will be discarded
nms_iou: 0.45  # When add_nms is True IoU threshold for NMS algorithm
               # (with smaller value more boxed will be considered "the same" and removed)
yolo_type: 'yolox'   # Type of yolo to build: 'yoloX' is th only type currently supported.
stem_type:  # 'focus' and '6x6' are supported, by default is defined by yolo_type and yolo_version

depthwise: False  # use depthwise separable convolutions all over the model
xhead_inter_channels:  # (has an impact only if yolo_type is yoloX)
# Channels in classification and regression branches of the detecting blocks
# if is None the first of input channels will be used by default
xhead_groups:  # (has an impact only if yolo_type is yoloX)
# Num groups in convs in classification and regression branches of the detecting blocks
# if None default groups will be used according to conv type
# (1 for Conv and depthwise for GroupedConvBlock)

_convert_: all
