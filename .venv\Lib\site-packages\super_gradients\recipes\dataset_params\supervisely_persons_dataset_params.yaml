dataset_dir: /data/supervisely-persons
batch_size: 8

train_dataset_params:
  root_dir: ${..dataset_dir}
  list_file: train.csv
  cache_labels: False
  cache_images: False
  transforms:
    - SegRandomRescale:
        scales: [ 0.25, 1. ]
    - SegColorJitter:
        brightness: 0.5
        contrast: 0.5
        saturation: 0.5
    - SegRandomFlip:
        prob: 0.5
    - SegPadShortToCropSize:
        crop_size: [ 320, 480 ]
        fill_mask: 0
    - SegCropImageAndMask:
        crop_size: [ 320, 480 ]
        mode: random

    - SegStandardize:
        max_value: 255

    - SegNormalize:
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]

    - SegConvertToTensor:
        mask_output_dtype: long



val_dataset_params:
  root_dir: ${..dataset_dir}
  list_file: val.csv
  cache_labels: False
  cache_images: False
  transforms:
    - SegResize:
        h: 480
        w: 320

    - SegStandardize:
        max_value: 255

    - SegNormalize:
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]

    - SegConvertToTensor:
        mask_output_dtype: long



train_dataloader_params:
  dataset: SuperviselyPersonsDataset
  shuffle: True
  batch_size: ${..batch_size}
  drop_last: True

val_dataloader_params:
  dataset: SuperviselyPersonsDataset
  batch_size: ${..batch_size}
  drop_last: False

_convert_: all
