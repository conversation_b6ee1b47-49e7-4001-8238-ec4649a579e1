# MobilNetV2 ImageNetDataset training recipe.
# Top1-Accuracy:  73.08
# Learning rate and batch size parameters, using 2 GPUs with DDP:
#     initial_lr: 0.032    batch-size: 256 * 2gpus = 512
#
# Instructions:
#   0. Make sure that the data is stored in dataset_params.dataset_dir or add "dataset_params.data_dir=<PATH-TO-DATASET>" at the end of the command below (feel free to check ReadMe)
#   1. Move to the project root (where you will find the ReadMe and src folder)
#   2. Run the command:
#       python -m super_gradients.train_from_recipe --config-name=imagenet_mobilenetv2

defaults:
  - training_hyperparams: imagenet_mobilenetv2_train_params
  - dataset_params: imagenet_mobilenetv2_dataset_params
  - arch_params: mobilenet_v2_arch_params
  - checkpoint_params: default_checkpoint_params
  - _self_
  - variable_setup

train_dataloader: imagenet_train
val_dataloader: imagenet_val

arch_params:
  num_classes: 1000
  dropout: 0.2


data_loader_num_workers: 8


resume: False
training_hyperparams:
  resume: ${resume}

experiment_name: mobileNetv2_training




multi_gpu: DDP
num_gpus: 2

architecture: mobilenet_v2
