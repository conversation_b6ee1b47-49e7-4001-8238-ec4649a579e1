# Barcodes > 2023-04-07 7:40pm
https://universe.roboflow.com/labeler-projects/barcodes-zmxjq

Provided by a Roboflow user
License: CC BY 4.0

Barcodes and QR codes are images with black and white components that, when scanned, can be coded to transmit information. Barcodes are machine-readable and often used in retail, logistics, manufacturing, point-of-sale, event ticketing, shipping, delivery, and asset management use cases.

This dataset uses bounding boxes for detection of barcodes and QR codes. Once a barcode is identified within an image, an application could trigger the reading of the barcode  or post-processing can be used to crop the barcode to be passed for an application to read the barcode. 

Barcodes can be read with computer vision using mobile devices such as iPhones or Android devices.

Research available on barcode recognition: https://link.springer.com/chapter/10.1007/978-3-030-57058-3_34

Github resources for barcode recognition: https://github.com/abbyy/barcode_detection_benchmark

How QR codes work: https://typefully.com/DanHollick/qr-codes-T7tLlNi