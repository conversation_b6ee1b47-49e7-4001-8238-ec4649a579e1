#!/usr/bin/env python3
"""
YOLO-NAS Barcode Detection Project Setup Script

This script sets up the complete environment for YOLO-NAS training including:
- Virtual environment creation with UV
- Dependency installation
- Directory structure creation
- Dataset conversion from COCO to YOLO format
- CUDA verification

Usage:
    python setup.py
    python setup.py --skip-conversion  # Skip dataset conversion
    python setup.py --cpu-only         # Setup for CPU-only training
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_command(command, check=True, shell=False):
    """Run a command and return the result."""
    try:
        if isinstance(command, str) and not shell:
            command = command.split()
        
        result = subprocess.run(command, check=check, capture_output=True, text=True, shell=shell)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {' '.join(command) if isinstance(command, list) else command}")
        logger.error(f"Error: {e.stderr}")
        return False, e.stdout, e.stderr
    except FileNotFoundError:
        logger.error(f"Command not found: {command[0] if isinstance(command, list) else command}")
        return False, "", "Command not found"


def check_uv_installation():
    """Check if UV is installed and install if necessary."""
    logger.info("Checking UV installation...")
    
    success, stdout, stderr = run_command("uv --version")
    
    if success:
        logger.info(f"UV is already installed: {stdout.strip()}")
        return True
    
    logger.info("UV not found. Installing UV...")
    
    # Try to install UV using pip
    success, stdout, stderr = run_command([sys.executable, "-m", "pip", "install", "uv"])
    
    if success:
        logger.info("UV installed successfully via pip")
        return True
    
    # Try alternative installation method for Windows
    if os.name == 'nt':
        logger.info("Trying alternative UV installation for Windows...")
        success, stdout, stderr = run_command(
            'powershell -c "irm https://astral.sh/uv/install.ps1 | iex"', 
            shell=True
        )
        
        if success:
            logger.info("UV installed successfully via PowerShell")
            return True
    
    logger.error("Failed to install UV. Please install manually:")
    logger.error("Visit: https://docs.astral.sh/uv/getting-started/installation/")
    return False


def create_virtual_environment():
    """Create virtual environment using UV."""
    logger.info("Creating virtual environment with UV...")
    
    # Remove existing virtual environment if it exists
    venv_path = Path(".venv")
    if venv_path.exists():
        logger.info("Removing existing virtual environment...")
        import shutil
        shutil.rmtree(venv_path)
    
    # Create new virtual environment
    success, stdout, stderr = run_command("uv venv")
    
    if success:
        logger.info("Virtual environment created successfully")
        return True
    else:
        logger.error(f"Failed to create virtual environment: {stderr}")
        return False


def install_dependencies(cpu_only=False):
    """Install project dependencies using UV."""
    logger.info("Installing dependencies...")
    
    # Base dependencies
    dependencies = [
        "super-gradients==3.1.1",
        "roboflow>=1.1.0",
        "supervision>=0.16.0",
        "tensorboard>=2.13.0",
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0",
        "opencv-python>=4.8.0",
        "pillow>=10.0.0",
        "numpy>=1.24.0",
        "pandas>=2.0.0",
        "pyyaml>=6.0",
        "tqdm>=4.65.0",
        "scikit-learn>=1.3.0",
        "albumentations>=1.3.0",
        "pycocotools>=2.0.6",
        "jupyter>=1.0.0",
        "ipykernel>=6.25.0",
    ]
    
    # PyTorch dependencies
    if cpu_only:
        torch_deps = [
            "torch>=2.0.0+cpu",
            "torchvision>=0.15.0+cpu",
            "torchaudio>=2.0.0+cpu"
        ]
        # Install PyTorch CPU version from specific index
        success, stdout, stderr = run_command([
            "uv", "pip", "install", "torch", "torchvision", "torchaudio", 
            "--index-url", "https://download.pytorch.org/whl/cpu"
        ])
        if not success:
            logger.error(f"Failed to install PyTorch CPU: {stderr}")
            return False
    else:
        torch_deps = [
            "torch>=2.0.0",
            "torchvision>=0.15.0",
            "torchaudio>=2.0.0"
        ]
        dependencies.extend(torch_deps)
    
    # Install dependencies
    for dep in dependencies:
        logger.info(f"Installing {dep}...")
        success, stdout, stderr = run_command(["uv", "pip", "install", dep])
        if not success:
            logger.error(f"Failed to install {dep}: {stderr}")
            return False
    
    logger.info("All dependencies installed successfully")
    return True


def create_directory_structure():
    """Create the project directory structure."""
    logger.info("Creating directory structure...")
    
    directories = [
        "data/raw",
        "data/processed/train/images",
        "data/processed/train/labels",
        "data/processed/val/images", 
        "data/processed/val/labels",
        "data/processed/test/images",
        "data/processed/test/labels",
        "data/splits",
        "models/pretrained",
        "models/checkpoints",
        "models/best",
        "configs/training",
        "configs/dataset",
        "configs/hyperparameters",
        "scripts/utils",
        "notebooks",
        "logs",
        "results/training",
        "results/evaluation",
        "docs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    logger.info("Directory structure created successfully")


def convert_dataset(skip_conversion=False):
    """Convert COCO dataset to YOLO format."""
    if skip_conversion:
        logger.info("Skipping dataset conversion")
        return True
    
    logger.info("Converting COCO dataset to YOLO format...")
    
    # Check if COCO dataset exists
    coco_dataset_path = Path("Barcodes.v5i.coco")
    if not coco_dataset_path.exists():
        logger.warning("COCO dataset not found. Skipping conversion.")
        logger.info("Please place your COCO dataset in the 'Barcodes.v5i.coco' directory")
        return True
    
    # Run conversion script
    success, stdout, stderr = run_command([
        "python", "scripts/utils/convert_coco_to_yolo.py",
        "--input", "Barcodes.v5i.coco",
        "--output", "data/processed"
    ])
    
    if success:
        logger.info("Dataset conversion completed successfully")
        logger.info(stdout)
        return True
    else:
        logger.error(f"Dataset conversion failed: {stderr}")
        return False


def verify_setup():
    """Verify the setup by running CUDA check."""
    logger.info("Verifying setup...")
    
    success, stdout, stderr = run_command(["python", "scripts/utils/check_cuda.py"])
    
    if success:
        logger.info("Setup verification completed")
        logger.info(stdout)
        return True
    else:
        logger.warning(f"Setup verification had issues: {stderr}")
        return False


def create_example_notebook():
    """Create an example Jupyter notebook for getting started."""
    notebook_content = '''{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# YOLO-NAS Barcode Detection - Getting Started\\n",
    "\\n",
    "This notebook provides a quick start guide for training and using YOLO-NAS models for barcode detection."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Environment Setup\\n",
    "\\n",
    "First, let's verify our environment is set up correctly:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Check CUDA setup\\n",
    "!python ../scripts/utils/check_cuda.py"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Dataset Conversion\\n",
    "\\n",
    "Convert COCO format dataset to YOLO format:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Convert dataset\\n",
    "!python ../scripts/utils/convert_coco_to_yolo.py --input ../Barcodes.v5i.coco --output ../data/processed"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Training\\n",
    "\\n",
    "Start training a YOLO-NAS model:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Start training\\n",
    "!python ../scripts/train.py --config ../configs/training/yolo_nas_default.yaml"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Evaluation\\n",
    "\\n",
    "Evaluate the trained model:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Evaluate model\\n",
    "!python ../scripts/evaluate.py --model ../models/best/model.pth --data ../data/processed/test"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Inference\\n",
    "\\n",
    "Run inference on new images:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Run inference\\n",
    "!python ../scripts/inference.py --model ../models/best/model.pth --input ../data/processed/test/images --output ../results/inference"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}'''
    
    notebook_path = Path("notebooks/getting_started.ipynb")
    with open(notebook_path, 'w') as f:
        f.write(notebook_content)
    
    logger.info(f"Example notebook created: {notebook_path}")


def main():
    parser = argparse.ArgumentParser(description='Setup YOLO-NAS Barcode Detection Project')
    parser.add_argument('--skip-conversion', action='store_true',
                        help='Skip dataset conversion step')
    parser.add_argument('--cpu-only', action='store_true',
                        help='Setup for CPU-only training (no CUDA)')
    parser.add_argument('--skip-verification', action='store_true',
                        help='Skip setup verification step')
    
    args = parser.parse_args()
    
    logger.info("Starting YOLO-NAS Barcode Detection Project Setup")
    logger.info("=" * 60)
    
    # Setup steps
    steps = [
        ("Checking UV installation", lambda: check_uv_installation()),
        ("Creating virtual environment", lambda: create_virtual_environment()),
        ("Installing dependencies", lambda: install_dependencies(args.cpu_only)),
        ("Creating directory structure", lambda: create_directory_structure()),
        ("Converting dataset", lambda: convert_dataset(args.skip_conversion)),
        ("Creating example notebook", lambda: create_example_notebook()),
    ]
    
    if not args.skip_verification:
        steps.append(("Verifying setup", lambda: verify_setup()))
    
    # Execute setup steps
    failed_steps = []
    
    for step_name, step_function in steps:
        logger.info(f"\\n{step_name}...")
        try:
            success = step_function()
            if success:
                logger.info(f"✅ {step_name} completed successfully")
            else:
                logger.error(f"❌ {step_name} failed")
                failed_steps.append(step_name)
        except Exception as e:
            logger.error(f"❌ {step_name} failed with exception: {e}")
            failed_steps.append(step_name)
    
    # Summary
    logger.info("\\n" + "=" * 60)
    logger.info("SETUP SUMMARY")
    logger.info("=" * 60)
    
    if not failed_steps:
        logger.info("🎉 Setup completed successfully!")
        logger.info("\\nNext steps:")
        logger.info("1. Activate the virtual environment:")
        if os.name == 'nt':
            logger.info("   .venv\\Scripts\\activate")
        else:
            logger.info("   source .venv/bin/activate")
        logger.info("2. Verify CUDA setup: python scripts/utils/check_cuda.py")
        logger.info("3. Convert your dataset: python scripts/utils/convert_coco_to_yolo.py")
        logger.info("4. Start training: python scripts/train.py")
        logger.info("5. Check the getting started notebook: notebooks/getting_started.ipynb")
    else:
        logger.error(f"❌ Setup completed with {len(failed_steps)} failed step(s):")
        for step in failed_steps:
            logger.error(f"   - {step}")
        logger.error("\\nPlease address the failed steps before proceeding.")
    
    logger.info("\\nFor more information, see README.md")


if __name__ == "__main__":
    main()
