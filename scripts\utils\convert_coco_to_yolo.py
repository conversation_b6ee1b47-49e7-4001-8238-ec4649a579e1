#!/usr/bin/env python3
"""
COCO to YOLO Format Converter

This script converts COCO format annotations to YOLO format for training.
It handles the barcode detection dataset structure and creates the necessary
directory structure for YOLO-NAS training.

Usage:
    python scripts/utils/convert_coco_to_yolo.py --input Barcodes.v5i.coco --output data/processed
"""

import argparse
import json
import os
import shutil
from pathlib import Path
from PIL import Image
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_coco_annotations(annotation_file):
    """Load COCO format annotations from JSON file."""
    try:
        with open(annotation_file, 'r') as f:
            coco_data = json.load(f)
        logger.info(f"Loaded COCO annotations from {annotation_file}")
        return coco_data
    except FileNotFoundError:
        logger.error(f"Annotation file not found: {annotation_file}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing JSON file: {e}")
        return None


def create_class_mapping(categories):
    """Create mapping from COCO category IDs to YOLO class indices."""
    class_mapping = {}
    class_names = []
    
    for i, category in enumerate(categories):
        class_mapping[category['id']] = i
        class_names.append(category['name'])
    
    logger.info(f"Created class mapping: {class_names}")
    return class_mapping, class_names


def convert_bbox_to_yolo(bbox, img_width, img_height):
    """
    Convert COCO bbox format to YOLO format.
    
    COCO format: [x_min, y_min, width, height] (absolute coordinates)
    YOLO format: [x_center, y_center, width, height] (normalized 0-1)
    """
    x_min, y_min, width, height = bbox
    
    # Calculate center coordinates
    x_center = x_min + width / 2
    y_center = y_min + height / 2
    
    # Normalize coordinates
    x_center_norm = x_center / img_width
    y_center_norm = y_center / img_height
    width_norm = width / img_width
    height_norm = height / img_height
    
    return [x_center_norm, y_center_norm, width_norm, height_norm]


def process_split(coco_data, split_name, input_dir, output_dir, class_mapping):
    """Process a single data split (train/val/test)."""
    images_input_dir = Path(input_dir) / split_name
    images_output_dir = Path(output_dir) / split_name / "images"
    labels_output_dir = Path(output_dir) / split_name / "labels"
    
    # Create output directories
    images_output_dir.mkdir(parents=True, exist_ok=True)
    labels_output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create image ID to annotations mapping
    image_annotations = {}
    for annotation in coco_data['annotations']:
        image_id = annotation['image_id']
        if image_id not in image_annotations:
            image_annotations[image_id] = []
        image_annotations[image_id].append(annotation)
    
    processed_images = 0
    processed_annotations = 0
    
    for image_info in coco_data['images']:
        image_id = image_info['id']
        image_filename = image_info['file_name']
        image_width = image_info['width']
        image_height = image_info['height']
        
        # Source and destination paths
        src_image_path = images_input_dir / image_filename
        dst_image_path = images_output_dir / image_filename
        
        # Check if source image exists
        if not src_image_path.exists():
            logger.warning(f"Image not found: {src_image_path}")
            continue
        
        # Copy image to output directory
        shutil.copy2(src_image_path, dst_image_path)
        
        # Create YOLO format label file
        label_filename = Path(image_filename).stem + '.txt'
        label_path = labels_output_dir / label_filename
        
        yolo_annotations = []
        
        # Process annotations for this image
        if image_id in image_annotations:
            for annotation in image_annotations[image_id]:
                category_id = annotation['category_id']
                bbox = annotation['bbox']
                
                # Convert to YOLO format
                if category_id in class_mapping:
                    class_id = class_mapping[category_id]
                    yolo_bbox = convert_bbox_to_yolo(bbox, image_width, image_height)
                    
                    # Format: class_id x_center y_center width height
                    yolo_line = f"{class_id} {' '.join(map(str, yolo_bbox))}"
                    yolo_annotations.append(yolo_line)
                    processed_annotations += 1
        
        # Write YOLO annotations to file
        with open(label_path, 'w') as f:
            f.write('\n'.join(yolo_annotations))
        
        processed_images += 1
        
        if processed_images % 100 == 0:
            logger.info(f"Processed {processed_images} images for {split_name}")
    
    logger.info(f"Completed {split_name}: {processed_images} images, {processed_annotations} annotations")
    return processed_images, processed_annotations


def create_dataset_yaml(output_dir, class_names):
    """Create dataset.yaml file for YOLO training."""
    dataset_config = {
        'path': str(Path(output_dir).absolute()),
        'train': 'train/images',
        'val': 'val/images',
        'test': 'test/images',
        'nc': len(class_names),
        'names': class_names
    }
    
    yaml_content = f"""# Barcode Detection Dataset Configuration
# Generated automatically from COCO format conversion

path: {dataset_config['path']}
train: {dataset_config['train']}
val: {dataset_config['val']}
test: {dataset_config['test']}

# Number of classes
nc: {dataset_config['nc']}

# Class names
names: {class_names}
"""
    
    yaml_path = Path(output_dir) / 'dataset.yaml'
    with open(yaml_path, 'w') as f:
        f.write(yaml_content)
    
    logger.info(f"Created dataset configuration: {yaml_path}")


def main():
    parser = argparse.ArgumentParser(description='Convert COCO format to YOLO format')
    parser.add_argument('--input', type=str, default='Barcodes.v5i.coco',
                        help='Input directory containing COCO format data')
    parser.add_argument('--output', type=str, default='data/processed',
                        help='Output directory for YOLO format data')
    parser.add_argument('--splits', nargs='+', default=['train', 'valid', 'test'],
                        help='Data splits to process')
    
    args = parser.parse_args()
    
    input_dir = Path(args.input)
    output_dir = Path(args.output)
    
    # Validate input directory
    if not input_dir.exists():
        logger.error(f"Input directory does not exist: {input_dir}")
        return
    
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    total_images = 0
    total_annotations = 0
    
    # Process each split
    for split in args.splits:
        annotation_file = input_dir / split / '_annotations.coco.json'
        
        if not annotation_file.exists():
            logger.warning(f"Annotation file not found for {split}: {annotation_file}")
            continue
        
        logger.info(f"Processing {split} split...")
        
        # Load COCO annotations
        coco_data = load_coco_annotations(annotation_file)
        if coco_data is None:
            continue
        
        # Create class mapping
        class_mapping, class_names = create_class_mapping(coco_data['categories'])
        
        # Process the split
        images_count, annotations_count = process_split(
            coco_data, split, input_dir, output_dir, class_mapping
        )
        
        total_images += images_count
        total_annotations += annotations_count
    
    # Create dataset configuration file
    if total_images > 0:
        create_dataset_yaml(output_dir, class_names)
        
        logger.info("Conversion completed successfully!")
        logger.info(f"Total images processed: {total_images}")
        logger.info(f"Total annotations processed: {total_annotations}")
        logger.info(f"Output directory: {output_dir}")
    else:
        logger.error("No images were processed. Please check your input data.")


if __name__ == "__main__":
    main()
