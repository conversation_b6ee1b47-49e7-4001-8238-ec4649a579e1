class_names: [
  "person",
  "bicycle",
  "car",
  "motorcycle",
  "airplane",
  "bus",
  "train",
  "truck",
  "boat",
  "traffic light",
  "fire hydrant",
  "stop sign",
  "parking meter",
  "bench",
  "bird",
  "cat",
  "dog",
  "horse",
  "sheep",
  "cow",
  "elephant",
  "bear",
  "zebra",
  "giraffe",
  "backpack",
  "umbrella",
  "handbag",
  "tie",
  "suitcase",
  "frisbee",
  "skis",
  "snowboard",
  "sports ball",
  "kite",
  "baseball bat",
  "baseball glove",
  "skateboard",
  "surfboard",
  "tennis racket",
  "bottle",
  "wine glass",
  "cup",
  "fork",
  "knife",
  "spoon",
  "bowl",
  "banana",
  "apple",
  "sandwich",
  "orange",
  "broccoli",
  "carrot",
  "hot dog",
  "pizza",
  "donut",
  "cake",
  "chair",
  "couch",
  "potted plant",
  "bed",
  "dining table",
  "toilet",
  "tv",
  "laptop",
  "mouse",
  "remote",
  "keyboard",
  "cell phone",
  "microwave",
  "oven",
  "toaster",
  "sink",
  "refrigerator",
  "book",
  "clock",
  "vase",
  "scissors",
  "teddy bear",
  "hair drier",
  "toothbrush",
]

train_dataset_params:
  data_dir: /data/coco # root path to coco data
  subdir: images/train2017 # sub directory path of data_dir containing the train data.
  json_file: instances_train2017.json # path to coco train json file, data_dir/annotations/train_json_file.
  input_dim: [640, 640]
  cache_annotations: True
  ignore_empty_annotations: True
  transforms:
    - DetectionRandomAffine:
        degrees: 0                    # rotation degrees, randomly sampled from [-degrees, degrees]
        translate: 0.25               # image translation fraction
        scales: [ 0.5, 1.5 ]          # random rescale range (keeps size by padding/cropping) after mosaic transform.
        shear: 0.0                    # shear degrees, randomly sampled from [-degrees, degrees]
        target_size:
        filter_box_candidates: True   # whether to filter out transformed bboxes by edge size, area ratio, and aspect ratio.
        wh_thr: 2                     # edge size threshold when filter_box_candidates = True (pixels)
        area_thr: 0.1                 # threshold for area ratio between original image and the transformed one, when when filter_box_candidates = True
        ar_thr: 20                    # aspect ratio threshold when filter_box_candidates = True
    - DetectionRGB2BGR:
        prob: 0.5
    - DetectionHSV:
        prob: 0.5                       # probability to apply HSV transform
        hgain: 18                       # HSV transform hue gain (randomly sampled from [-hgain, hgain])
        sgain: 30                       # HSV transform saturation gain (randomly sampled from [-sgain, sgain])
        vgain: 30                       # HSV transform value gain (randomly sampled from [-vgain, vgain])
    - DetectionHorizontalFlip:
        prob: 0.5                       # probability to apply horizontal flip
    - DetectionMixup:
        input_dim:
        mixup_scale: [ 0.5, 1.5 ]         # random rescale range for the additional sample in mixup
        prob: 0.5                       # probability to apply per-sample mixup
        flip_prob: 0.5                  # probability to apply horizontal flip
    - DetectionPaddedRescale:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
        pad_value: 114
    - DetectionStandardize:
        max_value: 255.
    - DetectionTargetsFormatTransform:
        output_format: LABEL_CXCYWH

  class_inclusion_list:
  max_num_samples:
  with_crowd: False

train_dataloader_params:
  batch_size: 25
  num_workers: 8
  shuffle: True
  drop_last: True
  pin_memory: True
  collate_fn: DetectionCollateFN

val_dataset_params:
  data_dir: /data/coco # root path to coco data
  subdir: images/val2017 # sub directory path of data_dir containing the train data.
  json_file: instances_val2017.json # path to coco train json file, data_dir/annotations/train_json_file.
  input_dim: [636, 636]
  cache_annotations: True
  ignore_empty_annotations: True
  transforms:
    - DetectionRGB2BGR:
        prob: 1
    - DetectionPadToSize:
        output_size: [640, 640]
        pad_value: 114
    - DetectionStandardize:
        max_value: 255.
    - DetectionImagePermute
    - DetectionTargetsFormatTransform:
        input_dim: [640, 640]
        output_format: LABEL_CXCYWH
  class_inclusion_list:
  max_num_samples:
  with_crowd: True

val_dataloader_params:
  batch_size: 25
  num_workers: 8
  drop_last: False
  shuffle: False
  pin_memory: True
  collate_fn: CrowdDetectionCollateFN

_convert_: all
