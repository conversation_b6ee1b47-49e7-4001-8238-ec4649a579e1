#!/usr/bin/env python3
"""
YOLO-NAS Training Script for Barcode Detection

This script provides a complete training pipeline for YOLO-NAS models
on custom datasets, specifically optimized for barcode detection.

Usage:
    python scripts/train.py --config configs/training/yolo_nas_default.yaml
    python scripts/train.py --model yolo_nas_s --epochs 100 --batch-size 16
"""

import argparse
import os
import sys
import yaml
import torch
import logging
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from super_gradients.training import Trainer
from super_gradients.training.dataloaders.dataloaders import (
    coco_detection_train,
    coco_detection_val,
)
from super_gradients.training.losses import PPYoloELoss
from super_gradients.training.metrics import DetectionMetrics_050
from super_gradients.training.models.detection_models.pp_yolo_e import (
    PPYoloEPostPredictionCallback,
)
from super_gradients.training import models

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("logs/training.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


def setup_directories():
    """Create necessary directories for training."""
    directories = [
        "logs",
        "models/checkpoints",
        "models/best",
        "results",
        "data/processed",
    ]
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)


def load_config(config_path):
    """Load training configuration from YAML file."""
    try:
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        logger.info(f"Loaded configuration from {config_path}")
        return config
    except FileNotFoundError:
        logger.error(f"Configuration file not found: {config_path}")
        sys.exit(1)
    except yaml.YAMLError as e:
        logger.error(f"Error parsing configuration file: {e}")
        sys.exit(1)


def check_cuda():
    """Check CUDA availability and log GPU information."""
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        current_gpu = torch.cuda.current_device()
        gpu_name = torch.cuda.get_device_name(current_gpu)
        logger.info(f"CUDA available: {gpu_count} GPU(s)")
        logger.info(f"Current GPU: {gpu_name}")
        logger.info(f"CUDA version: {torch.version.cuda}")
        return True
    else:
        logger.warning("CUDA not available. Training will use CPU (much slower).")
        return False


def prepare_dataset_config(config):
    """Prepare dataset configuration for YOLO-NAS training."""
    dataset_config = {
        "data_dir": config["dataset"]["data_dir"],
        "train_images_dir": config["dataset"]["train_images_dir"],
        "train_annotations": config["dataset"]["train_annotations"],
        "val_images_dir": config["dataset"]["val_images_dir"],
        "val_annotations": config["dataset"]["val_annotations"],
        "test_images_dir": config["dataset"].get("test_images_dir", ""),
        "test_annotations": config["dataset"].get("test_annotations", ""),
        "classes": config["dataset"]["classes"],
    }

    # Validate paths
    for key, path in dataset_config.items():
        if key.endswith("_dir") and path and not os.path.exists(path):
            logger.error(f"Dataset path does not exist: {path}")
            sys.exit(1)
        elif key.endswith("_annotations") and path and not os.path.exists(path):
            logger.error(f"Annotation file does not exist: {path}")
            sys.exit(1)

    return dataset_config


def create_dataloaders(dataset_config, training_config):
    """Create training and validation dataloaders for COCO format."""
    train_data = coco_detection_train(
        dataset_params={
            "data_dir": dataset_config["data_dir"],
            "images_dir": dataset_config["train_images_dir"],
            "json_annotation_file": dataset_config["train_annotations"],
            "classes": dataset_config["classes"],
        },
        dataloader_params={
            "batch_size": training_config["batch_size"],
            "num_workers": training_config.get("num_workers", 4),
            "shuffle": True,
            "drop_last": True,
            "pin_memory": True,
        },
    )

    val_data = coco_detection_val(
        dataset_params={
            "data_dir": dataset_config["data_dir"],
            "images_dir": dataset_config["val_images_dir"],
            "json_annotation_file": dataset_config["val_annotations"],
            "classes": dataset_config["classes"],
        },
        dataloader_params={
            "batch_size": training_config["batch_size"],
            "num_workers": training_config.get("num_workers", 4),
            "shuffle": False,
            "drop_last": False,
            "pin_memory": True,
        },
    )

    logger.info(
        f"Created dataloaders - Train batches: {len(train_data)}, Val batches: {len(val_data)}"
    )
    return train_data, val_data


def setup_model(model_config, num_classes):
    """Setup YOLO-NAS model with specified configuration."""
    model_name = model_config["name"]
    pretrained_weights = model_config.get("pretrained_weights", "coco")

    logger.info(f"Loading model: {model_name}")
    logger.info(f"Pretrained weights: {pretrained_weights}")

    model = models.get(
        model_name=model_name,
        num_classes=num_classes,
        pretrained_weights=pretrained_weights,
    )

    return model


def setup_training_params(training_config, num_classes):
    """Setup training parameters including loss, metrics, and callbacks."""
    training_params = {
        "silent_mode": False,
        "average_best_models": True,
        "warmup_mode": "linear_epoch_step",
        "warmup_initial_lr": 1e-6,
        "lr_warmup_epochs": 3,
        "initial_lr": training_config["learning_rate"],
        "lr_mode": "cosine",
        "cosine_final_lr_ratio": 0.1,
        "optimizer": "AdamW",
        "optimizer_params": {"weight_decay": 0.0001},
        "zero_weight_decay_on_bias_and_bn": True,
        "ema": True,
        "ema_params": {"decay": 0.9, "decay_type": "threshold"},
        "max_epochs": training_config["epochs"],
        "mixed_precision": training_config.get("mixed_precision", True),
        "loss": PPYoloELoss(
            use_static_assigner=False, num_classes=num_classes, reg_max=16
        ),
        "valid_metrics_list": [
            DetectionMetrics_050(
                score_thres=0.1,
                top_k_predictions=300,
                num_cls=num_classes,
                normalize_targets=True,
                post_prediction_callback=PPYoloEPostPredictionCallback(
                    score_threshold=0.01,
                    nms_top_k=1000,
                    max_predictions=300,
                    nms_threshold=0.7,
                ),
            )
        ],
        "metric_to_watch": "mAP@0.50",
        "greater_metric_to_watch_is_better": True,
    }

    return training_params


def main():
    parser = argparse.ArgumentParser(
        description="Train YOLO-NAS model for barcode detection"
    )
    parser.add_argument(
        "--config",
        type=str,
        default="configs/training/yolo_nas_default.yaml",
        help="Path to training configuration file",
    )
    parser.add_argument(
        "--model",
        type=str,
        choices=["yolo_nas_s", "yolo_nas_m", "yolo_nas_l"],
        help="Model architecture (overrides config)",
    )
    parser.add_argument(
        "--epochs", type=int, help="Number of training epochs (overrides config)"
    )
    parser.add_argument("--batch-size", type=int, help="Batch size (overrides config)")
    parser.add_argument("--lr", type=float, help="Learning rate (overrides config)")
    parser.add_argument(
        "--experiment-name", type=str, help="Experiment name for logging"
    )

    args = parser.parse_args()

    # Setup directories
    setup_directories()

    # Check CUDA
    cuda_available = check_cuda()

    # Load configuration
    config = load_config(args.config)

    # Override config with command line arguments
    if args.model:
        config["model"]["name"] = args.model
    if args.epochs:
        config["training"]["epochs"] = args.epochs
    if args.batch_size:
        config["training"]["batch_size"] = args.batch_size
    if args.lr:
        config["training"]["learning_rate"] = args.lr

    # Setup experiment name
    experiment_name = (
        args.experiment_name
        or f"{config['model']['name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )

    logger.info(f"Starting training experiment: {experiment_name}")
    logger.info(f"Model: {config['model']['name']}")
    logger.info(f"Epochs: {config['training']['epochs']}")
    logger.info(f"Batch size: {config['training']['batch_size']}")
    logger.info(f"Learning rate: {config['training']['learning_rate']}")

    # Prepare dataset
    dataset_config = prepare_dataset_config(config)
    num_classes = len(dataset_config["classes"])
    logger.info(f"Number of classes: {num_classes}")
    logger.info(f"Classes: {dataset_config['classes']}")

    # Create dataloaders
    train_data, val_data = create_dataloaders(dataset_config, config["training"])

    # Setup model
    model = setup_model(config["model"], num_classes)

    # Setup trainer
    trainer = Trainer(
        experiment_name=experiment_name, ckpt_root_dir="models/checkpoints"
    )

    # Setup training parameters
    training_params = setup_training_params(config["training"], num_classes)

    # Start training
    logger.info("Starting training...")
    try:
        trainer.train(
            model=model,
            training_params=training_params,
            train_loader=train_data,
            valid_loader=val_data,
        )

        logger.info("Training completed successfully!")

        # Save best model
        best_model_path = f"models/best/{experiment_name}_best.pth"
        trainer.save_model(best_model_path)
        logger.info(f"Best model saved to: {best_model_path}")

    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
