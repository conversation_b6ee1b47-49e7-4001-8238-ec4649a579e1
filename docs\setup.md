# Setup Guide

This guide will walk you through setting up the YOLO-NAS barcode detection training environment.

## Prerequisites

- Python 3.8 or higher
- NVIDIA GPU with CUDA support (recommended)
- At least 8GB of GPU memory for optimal training
- Windows, macOS, or Linux operating system

## Quick Setup

The easiest way to set up the project is using the automated setup script:

```bash
python setup.py
```

This will:
- Install UV package manager
- Create a virtual environment
- Install all dependencies
- Create the directory structure
- Convert your COCO dataset to YOLO format
- Verify CUDA setup

## Manual Setup

If you prefer to set up manually or encounter issues with the automated setup:

### 1. Install UV Package Manager

UV is a fast Python package manager that we use for dependency management.

**Windows:**
```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

**macOS/Linux:**
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

**Alternative (using pip):**
```bash
pip install uv
```

### 2. Create Virtual Environment

```bash
uv venv
```

### 3. Activate Virtual Environment

**Windows:**
```cmd
.venv\Scripts\activate
```

**macOS/Linux:**
```bash
source .venv/bin/activate
```

### 4. Install Dependencies

```bash
uv pip install -r requirements.txt
```

For CPU-only training (no CUDA):
```bash
uv pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
uv pip install -r requirements.txt
```

### 5. Verify Installation

Check if everything is installed correctly:

```bash
python scripts/utils/check_cuda.py
```

## Directory Structure

After setup, your project should have this structure:

```
barcode_detection_benchmark/
├── .venv/                      # Virtual environment
├── data/                       # Dataset storage
│   ├── raw/                   # Original COCO format data
│   ├── processed/             # Processed YOLO format data
│   └── splits/                # Train/val/test splits
├── models/                     # Model storage
│   ├── pretrained/            # Pre-trained models
│   ├── checkpoints/           # Training checkpoints
│   └── best/                  # Best trained models
├── configs/                    # Configuration files
├── scripts/                    # Training and utility scripts
├── notebooks/                  # Jupyter notebooks
├── logs/                      # Training logs
├── results/                   # Training results
└── docs/                      # Documentation
```

## Dataset Preparation

### COCO to YOLO Conversion

If you have a COCO format dataset (like the provided barcode dataset), convert it to YOLO format:

```bash
python scripts/utils/convert_coco_to_yolo.py --input Barcodes.v5i.coco --output data/processed
```

### Custom Dataset

For your own dataset, ensure it follows this structure:

```
data/processed/
├── train/
│   ├── images/
│   └── labels/
├── val/
│   ├── images/
│   └── labels/
└── test/
    ├── images/
    └── labels/
```

Label files should be in YOLO format:
```
class_id x_center y_center width height
```

Where coordinates are normalized (0-1).

## Hardware Requirements

### Minimum Requirements
- 4GB GPU memory
- 8GB system RAM
- 10GB free disk space

### Recommended Requirements
- 8GB+ GPU memory (RTX 3070, RTX 4060, or better)
- 16GB+ system RAM
- 50GB+ free disk space
- SSD storage for faster data loading

### GPU Compatibility

Supported GPUs:
- NVIDIA RTX series (20xx, 30xx, 40xx)
- NVIDIA GTX 1060 or newer
- NVIDIA Tesla/Quadro series
- Any GPU with CUDA Compute Capability 6.0+

Check your GPU compatibility:
```bash
nvidia-smi
python scripts/utils/check_cuda.py
```

## Troubleshooting

### Common Issues

**1. CUDA not available**
- Ensure NVIDIA drivers are installed
- Install CUDA toolkit
- Verify PyTorch CUDA installation

**2. Out of memory errors**
- Reduce batch size in config
- Use gradient accumulation
- Enable mixed precision training

**3. UV installation fails**
- Try alternative installation methods
- Use pip as fallback: `pip install uv`
- Check internet connection and firewall

**4. Package installation errors**
- Update pip: `python -m pip install --upgrade pip`
- Clear pip cache: `pip cache purge`
- Try installing packages individually

### Getting Help

1. Check the logs in `logs/` directory
2. Run the CUDA verification script
3. Review the troubleshooting guide in `docs/troubleshooting.md`
4. Check GitHub issues for similar problems

## Next Steps

After successful setup:

1. **Verify everything works:**
   ```bash
   python scripts/utils/check_cuda.py
   ```

2. **Convert your dataset:**
   ```bash
   python scripts/utils/convert_coco_to_yolo.py
   ```

3. **Start training:**
   ```bash
   python scripts/train.py --config configs/training/yolo_nas_default.yaml
   ```

4. **Explore the example notebook:**
   ```bash
   jupyter notebook notebooks/getting_started.ipynb
   ```

## Configuration

The main configuration file is `configs/training/yolo_nas_default.yaml`. Key settings:

- `model.name`: Choose from `yolo_nas_s`, `yolo_nas_m`, `yolo_nas_l`
- `training.batch_size`: Adjust based on GPU memory
- `training.epochs`: Number of training epochs
- `training.learning_rate`: Learning rate for optimization

See `docs/configuration.md` for detailed configuration options.
