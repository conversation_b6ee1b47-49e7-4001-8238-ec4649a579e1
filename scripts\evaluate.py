#!/usr/bin/env python3
"""
YOLO-NAS Model Evaluation Script

This script evaluates trained YOLO-NAS models on test datasets and generates
comprehensive evaluation reports including metrics, visualizations, and analysis.

Usage:
    python scripts/evaluate.py --model models/best/model.pth --data data/processed/test
    python scripts/evaluate.py --checkpoint models/checkpoints/experiment/ckpt_best.pth
"""

import argparse
import os
import sys
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime
import logging

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from super_gradients.training import models
from super_gradients.training.dataloaders.dataloaders import coco_detection_yolo_format_val
from super_gradients.training.metrics import DetectionMetrics_050, DetectionMetrics_050_095
from super_gradients.training.models.detection_models.pp_yolo_e import PPYoloEPostPredictionCallback

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_model(model_path, num_classes=2):
    """Load trained YOLO-NAS model from checkpoint."""
    try:
        if model_path.endswith('.pth'):
            # Load from saved model file
            model = models.get('yolo_nas_s', num_classes=num_classes, pretrained_weights=None)
            checkpoint = torch.load(model_path, map_location='cpu')
            model.load_state_dict(checkpoint)
        else:
            # Load from checkpoint directory
            from super_gradients.training import Trainer
            trainer = Trainer(experiment_name="evaluation", ckpt_root_dir=str(Path(model_path).parent))
            model = trainer.load_model(model_path)
        
        model.eval()
        logger.info(f"Model loaded successfully from {model_path}")
        return model
        
    except Exception as e:
        logger.error(f"Error loading model: {e}")
        return None


def create_dataloader(data_dir, batch_size=8):
    """Create evaluation dataloader."""
    try:
        # Determine class names from dataset
        dataset_yaml = Path(data_dir).parent / 'dataset.yaml'
        if dataset_yaml.exists():
            import yaml
            with open(dataset_yaml, 'r') as f:
                dataset_config = yaml.safe_load(f)
            classes = dataset_config['names']
        else:
            classes = ['Barcode', 'QR Code']  # Default classes
        
        dataloader = coco_detection_yolo_format_val(
            dataset_params={
                'data_dir': str(Path(data_dir).parent),
                'images_dir': str(Path(data_dir) / 'images'),
                'labels_dir': str(Path(data_dir) / 'labels'),
                'classes': classes
            },
            dataloader_params={
                'batch_size': batch_size,
                'num_workers': 4,
                'shuffle': False,
                'drop_last': False,
                'pin_memory': True
            }
        )
        
        logger.info(f"Created dataloader with {len(dataloader)} batches")
        return dataloader, classes
        
    except Exception as e:
        logger.error(f"Error creating dataloader: {e}")
        return None, None


def evaluate_model(model, dataloader, device='cuda'):
    """Evaluate model on test dataset."""
    logger.info("Starting model evaluation...")
    
    # Setup metrics
    metrics_050 = DetectionMetrics_050(
        score_thres=0.1,
        top_k_predictions=300,
        num_cls=len(dataloader.dataset.classes),
        normalize_targets=True,
        post_prediction_callback=PPYoloEPostPredictionCallback(
            score_threshold=0.01,
            nms_top_k=1000,
            max_predictions=300,
            nms_threshold=0.7
        )
    )
    
    metrics_050_095 = DetectionMetrics_050_095(
        score_thres=0.1,
        top_k_predictions=300,
        num_cls=len(dataloader.dataset.classes),
        normalize_targets=True,
        post_prediction_callback=PPYoloEPostPredictionCallback(
            score_threshold=0.01,
            nms_top_k=1000,
            max_predictions=300,
            nms_threshold=0.7
        )
    )
    
    model.to(device)
    model.eval()
    
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for batch_idx, (images, targets) in enumerate(dataloader):
            if isinstance(images, list):
                images = torch.stack(images)
            
            images = images.to(device)
            
            # Get predictions
            predictions = model(images)
            
            # Store for metrics calculation
            all_predictions.extend(predictions)
            all_targets.extend(targets)
            
            if batch_idx % 10 == 0:
                logger.info(f"Processed batch {batch_idx}/{len(dataloader)}")
    
    # Calculate metrics
    logger.info("Calculating metrics...")
    
    # Convert to format expected by metrics
    predictions_tensor = torch.stack(all_predictions) if all_predictions else torch.empty(0)
    targets_tensor = torch.stack(all_targets) if all_targets else torch.empty(0)
    
    metrics_050.update(predictions_tensor, targets_tensor)
    metrics_050_095.update(predictions_tensor, targets_tensor)
    
    results_050 = metrics_050.compute()
    results_050_095 = metrics_050_095.compute()
    
    # Combine results
    evaluation_results = {
        'mAP@0.50': float(results_050.get('mAP@0.50', 0)),
        'mAP@0.50:0.95': float(results_050_095.get('mAP@0.50:0.95', 0)),
        'Precision': float(results_050.get('Precision', 0)),
        'Recall': float(results_050.get('Recall', 0)),
        'F1': float(results_050.get('F1', 0)),
        'num_samples': len(all_predictions)
    }
    
    logger.info("Evaluation completed")
    return evaluation_results


def generate_evaluation_report(results, classes, output_dir):
    """Generate comprehensive evaluation report."""
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create report
    report = {
        'evaluation_date': datetime.now().isoformat(),
        'metrics': results,
        'classes': classes,
        'summary': {
            'total_samples': results['num_samples'],
            'overall_performance': 'Good' if results['mAP@0.50'] > 0.7 else 'Needs Improvement'
        }
    }
    
    # Save JSON report
    json_path = output_dir / 'evaluation_report.json'
    with open(json_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    # Generate visualizations
    create_metrics_visualization(results, output_dir)
    
    # Generate text report
    create_text_report(report, output_dir)
    
    logger.info(f"Evaluation report saved to {output_dir}")


def create_metrics_visualization(results, output_dir):
    """Create visualization of evaluation metrics."""
    plt.style.use('seaborn-v0_8')
    
    # Metrics bar chart
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Main metrics
    metrics = ['mAP@0.50', 'mAP@0.50:0.95', 'Precision', 'Recall', 'F1']
    values = [results.get(metric, 0) for metric in metrics]
    
    bars = ax1.bar(metrics, values, color=['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83'])
    ax1.set_title('Model Performance Metrics', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Score', fontsize=12)
    ax1.set_ylim(0, 1)
    
    # Add value labels on bars
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # Performance gauge
    mAP_50 = results.get('mAP@0.50', 0)
    colors = ['#C73E1D', '#F18F01', '#2E86AB']
    sizes = [max(0, 0.5 - mAP_50), max(0, min(0.3, mAP_50 - 0.5)), max(0, mAP_50 - 0.8)]
    labels = ['Poor (<0.5)', 'Good (0.5-0.8)', 'Excellent (>0.8)']
    
    wedges, texts, autotexts = ax2.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                      startangle=90, counterclock=False)
    ax2.set_title(f'mAP@0.50 Performance\n{mAP_50:.3f}', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'metrics_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info("Metrics visualization saved")


def create_text_report(report, output_dir):
    """Create detailed text report."""
    report_text = f"""
YOLO-NAS Barcode Detection Model Evaluation Report
================================================

Evaluation Date: {report['evaluation_date']}
Total Samples: {report['summary']['total_samples']}

PERFORMANCE METRICS
==================
mAP@0.50:      {report['metrics']['mAP@0.50']:.4f}
mAP@0.50:0.95: {report['metrics']['mAP@0.50:0.95']:.4f}
Precision:     {report['metrics']['Precision']:.4f}
Recall:        {report['metrics']['Recall']:.4f}
F1 Score:      {report['metrics']['F1']:.4f}

CLASSES
=======
{', '.join(report['classes'])}

PERFORMANCE ANALYSIS
===================
Overall Performance: {report['summary']['overall_performance']}

mAP@0.50 Analysis:
- Excellent (>0.8): {'✓' if report['metrics']['mAP@0.50'] > 0.8 else '✗'}
- Good (0.5-0.8):   {'✓' if 0.5 <= report['metrics']['mAP@0.50'] <= 0.8 else '✗'}
- Needs Work (<0.5): {'✓' if report['metrics']['mAP@0.50'] < 0.5 else '✗'}

RECOMMENDATIONS
==============
"""
    
    # Add recommendations based on performance
    mAP_50 = report['metrics']['mAP@0.50']
    if mAP_50 > 0.8:
        report_text += "- Excellent performance! Model is ready for production.\n"
        report_text += "- Consider fine-tuning for specific use cases.\n"
    elif mAP_50 > 0.5:
        report_text += "- Good performance, but room for improvement.\n"
        report_text += "- Consider additional training epochs or data augmentation.\n"
        report_text += "- Review difficult cases and add more training data.\n"
    else:
        report_text += "- Performance needs significant improvement.\n"
        report_text += "- Review training data quality and quantity.\n"
        report_text += "- Consider different model architecture or hyperparameters.\n"
        report_text += "- Increase training epochs and improve data augmentation.\n"
    
    # Save text report
    with open(output_dir / 'evaluation_report.txt', 'w') as f:
        f.write(report_text)
    
    logger.info("Text report saved")


def main():
    parser = argparse.ArgumentParser(description='Evaluate YOLO-NAS model')
    parser.add_argument('--model', type=str, required=True,
                        help='Path to trained model or checkpoint')
    parser.add_argument('--data', type=str, required=True,
                        help='Path to test data directory')
    parser.add_argument('--output', type=str, default='results/evaluation',
                        help='Output directory for evaluation results')
    parser.add_argument('--batch-size', type=int, default=8,
                        help='Batch size for evaluation')
    parser.add_argument('--device', type=str, default='auto',
                        help='Device to use (auto, cpu, cuda)')
    
    args = parser.parse_args()
    
    # Setup device
    if args.device == 'auto':
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    else:
        device = args.device
    
    logger.info(f"Using device: {device}")
    
    # Load model
    model = load_model(args.model)
    if model is None:
        sys.exit(1)
    
    # Create dataloader
    dataloader, classes = create_dataloader(args.data, args.batch_size)
    if dataloader is None:
        sys.exit(1)
    
    # Evaluate model
    results = evaluate_model(model, dataloader, device)
    
    # Generate report
    generate_evaluation_report(results, classes, args.output)
    
    # Print summary
    print("\nEVALUATION SUMMARY")
    print("="*50)
    print(f"mAP@0.50:      {results['mAP@0.50']:.4f}")
    print(f"mAP@0.50:0.95: {results['mAP@0.50:0.95']:.4f}")
    print(f"Precision:     {results['Precision']:.4f}")
    print(f"Recall:        {results['Recall']:.4f}")
    print(f"F1 Score:      {results['F1']:.4f}")
    print(f"Samples:       {results['num_samples']}")
    print(f"\nDetailed report saved to: {args.output}")


if __name__ == "__main__":
    main()
