cdef extern from "Python.h":

    #####################################################################
    # 9.2 Memory Interface
    #####################################################################
    # You are definitely *supposed* to use these: "In most situations,
    # however, it is recommended to allocate memory from the Python
    # heap specifically because the latter is under control of the
    # Python memory manager. For example, this is required when the
    # interpreter is extended with new object types written in
    # C. Another reason for using the Python heap is the desire to
    # inform the Python memory manager about the memory needs of the
    # extension module. Even when the requested memory is used
    # exclusively for internal, highly-specific purposes, delegating
    # all memory requests to the Python memory manager causes the
    # interpreter to have a more accurate image of its memory
    # footprint as a whole. Consequently, under certain circumstances,
    # the Python memory manager may or may not trigger appropriate
    # actions, like garbage collection, memory compaction or other
    # preventive procedures. Note that by using the C library
    # allocator as shown in the previous example, the allocated memory
    # for the I/O buffer escapes completely the Python memory
    # manager."

    # The following function sets, modeled after the ANSI C standard,
    # but specifying behavior when requesting zero bytes, are
    # available for allocating and releasing memory from the Python
    # heap:

    void* PyMem_RawMalloc(size_t n) nogil
    void* PyMem_Malloc(size_t n)
    # Allocates n bytes and returns a pointer of type void* to the
    # allocated memory, or NULL if the request fails. Requesting zero
    # bytes returns a distinct non-NULL pointer if possible, as if
    # PyMem_Malloc(1) had been called instead. The memory will not
    # have been initialized in any way.

    void* PyMem_RawCalloc(size_t nelem, size_t elsize) nogil
    void* PyMem_Calloc(size_t nelem, size_t elsize)
    # Allocates nelem elements each whose size in bytes is elsize and
    # returns a pointer of type void* to the allocated memory, or NULL if
    # the request fails. The memory is initialized to zeros. Requesting
    # zero elements or elements of size zero bytes returns a distinct
    # non-NULL pointer if possible, as if PyMem_Calloc(1, 1) had been
    # called instead.

    void* PyMem_RawRealloc(void *p, size_t n) nogil
    void* PyMem_Realloc(void *p, size_t n)
    # Resizes the memory block pointed to by p to n bytes. The
    # contents will be unchanged to the minimum of the old and the new
    # sizes. If p is NULL, the call is equivalent to PyMem_Malloc(n);
    # else if n is equal to zero, the memory block is resized but is
    # not freed, and the returned pointer is non-NULL. Unless p is
    # NULL, it must have been returned by a previous call to
    # PyMem_Malloc(), PyMem_Realloc(), or PyMem_Calloc().

    void PyMem_RawFree(void *p) nogil
    void PyMem_Free(void *p)
    # Frees the memory block pointed to by p, which must have been
    # returned by a previous call to PyMem_Malloc(), PyMem_Realloc(), or
    # PyMem_Calloc(). Otherwise, or if PyMem_Free(p) has been called
    # before, undefined behavior occurs. If p is NULL, no operation is
    # performed.

    # The following type-oriented macros are provided for
    # convenience. Note that TYPE refers to any C type.

    # TYPE* PyMem_New(TYPE, size_t n)
    # Same as PyMem_Malloc(), but allocates (n * sizeof(TYPE)) bytes
    # of memory. Returns a pointer cast to TYPE*. The memory will not
    # have been initialized in any way.

    # TYPE* PyMem_Resize(void *p, TYPE, size_t n)
    # Same as PyMem_Realloc(), but the memory block is resized to (n *
    # sizeof(TYPE)) bytes. Returns a pointer cast to TYPE*.

    void PyMem_Del(void *p)
    # Same as PyMem_Free().

    # In addition, the following macro sets are provided for calling
    # the Python memory allocator directly, without involving the C
    # API functions listed above. However, note that their use does
    # not preserve binary compatibility across Python versions and is
    # therefore deprecated in extension modules.

    # PyMem_MALLOC(), PyMem_REALLOC(), PyMem_FREE().
    # PyMem_NEW(), PyMem_RESIZE(), PyMem_DEL().


    #####################################################################
    # Raw object memory interface
    #####################################################################

    # Functions to call the same malloc/realloc/free as used by Python's
    # object allocator.  If WITH_PYMALLOC is enabled, these may differ from
    # the platform malloc/realloc/free.  The Python object allocator is
    # designed for fast, cache-conscious allocation of many "small" objects,
    # and with low hidden memory overhead.
    #
    # PyObject_Malloc(0) returns a unique non-NULL pointer if possible.
    #
    # PyObject_Realloc(NULL, n) acts like PyObject_Malloc(n).
    # PyObject_Realloc(p != NULL, 0) does not return  NULL, or free the memory
    # at p.
    #
    # Returned pointers must be checked for NULL explicitly; no action is
    # performed on failure other than to return NULL (no warning it printed, no
    # exception is set, etc).
    #
    # For allocating objects, use PyObject_{New, NewVar} instead whenever
    # possible.  The PyObject_{Malloc, Realloc, Free} family is exposed
    # so that you can exploit Python's small-block allocator for non-object
    # uses.  If you must use these routines to allocate object memory, make sure
    # the object gets initialized via PyObject_{Init, InitVar} after obtaining
    # the raw memory.

    void* PyObject_Malloc(size_t size)
    void* PyObject_Calloc(size_t nelem, size_t elsize)
    void* PyObject_Realloc(void *ptr, size_t new_size)
    void PyObject_Free(void *ptr)
