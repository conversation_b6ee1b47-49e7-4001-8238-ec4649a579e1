defaults:
  - coco_pose_estimation_common_dataset_params
  - _self_


train_dataset_params:
  data_dir: /data/coco # root path to coco data
  images_dir: images/train2017
  json_file: annotations/person_keypoints_train2017.json

  include_empty_samples: False
  min_instance_area: 64

  edge_links: ${dataset_params.edge_links}
  edge_colors: ${dataset_params.edge_colors}
  keypoint_colors: ${dataset_params.keypoint_colors}

  transforms:
    - KeypointsLongestMaxSize:
        max_height: 640
        max_width: 640

    - KeypointsPadIfNeeded:
        min_height: 640
        min_width: 640
        image_pad_value: 127
        mask_pad_value: 1

    - KeypointsRandomHorizontalFlip:
        # Note these indexes are COCO-specific. If you're using a different dataset, you'll need to change these accordingly.
        flip_index: ${dataset_params.flip_indexes}
        prob: 0.5

    - KeypointsRandomAffineTransform:
        max_rotation: 30
        min_scale: 0.5
        max_scale: 2
        max_translate: 0.2
        image_pad_value: 127
        mask_pad_value: 1
        prob: 0.75

    - KeypointsImageStandardize:
        max_value: 255

    - KeypointsImageNormalize:
        mean: [ 0.485, 0.456, 0.406 ]
        std: [ 0.229, 0.224, 0.225 ]

    - KeypointsImageToTensor

val_dataset_params:
  data_dir: /data/coco/

  images_dir: images/val2017
  json_file: annotations/person_keypoints_val2017.json
  include_empty_samples: True
  min_instance_area: 128

  edge_links: ${dataset_params.edge_links}
  edge_colors: ${dataset_params.edge_colors}
  keypoint_colors: ${dataset_params.keypoint_colors}

  transforms:
    - KeypointsLongestMaxSize:
        max_height: 640
        max_width: 640

    - KeypointsPadIfNeeded:
        min_height: 640
        min_width: 640
        image_pad_value: 127
        mask_pad_value: 1

    - KeypointsImageStandardize:
        max_value: 255

    - KeypointsImageNormalize:
        mean: [ 0.485, 0.456, 0.406 ]
        std: [ 0.229, 0.224, 0.225 ]

    - KeypointsImageToTensor

train_dataloader_params:
  shuffle: True
  batch_size: 8
  num_workers: 8
  drop_last: True
  collate_fn: KeypointsCollate

val_dataloader_params:
  batch_size: 24
  num_workers: 8
  drop_last: False
  collate_fn: KeypointsCollate

_convert_: all
