defaults:
  - imagenet_dataset_params

train_dataset_params:
  root: /data/Imagenet/train
  transforms:
    - RandomResizedCropAndInterpolation:
        size: 224
        interpolation: random
    - RandomHorizontalFlip
    - RandAugmentTransform:
        config_str: rand-m7-mstd0.5
        crop_size: 224
        img_mean: ${dataset_params.img_mean}  # Use default value from imagenet_dataset_params
    - ToTensor
    - Normalize:
        mean: ${dataset_params.img_mean}      # Use default value from imagenet_dataset_params
        std: ${dataset_params.img_std}        # Use default value from imagenet_dataset_params

val_dataset_params:
  root: /data/Imagenet/val
  transforms:
    - Resize:
        size: 236
    - CenterCrop:
        size: 224
    - ToTensor
    - Normalize:
        mean: ${dataset_params.img_mean}
        std: ${dataset_params.img_std}

train_dataloader_params:
  batch_size: 236
  collate_fn:
    _target_: super_gradients.training.datasets.mixup.CollateMixup
    mixup_alpha: 0.2
    cutmix_alpha: 1.0
    label_smoothing: 0.1
