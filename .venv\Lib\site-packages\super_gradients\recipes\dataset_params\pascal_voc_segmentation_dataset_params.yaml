train_dataset_params:
  root: /data/pascal_voc_2012
  list_file: VOCdevkit/VOC2012/ImageSets/Segmentation/train.txt
  samples_sub_directory: VOCdevkit/VOC2012/JPEGImages
  targets_sub_directory: VOCdevkit/VOC2012/SegmentationClass
  cache_labels: False
  cache_images: False
  transforms:
    # for more options see common.factories.transforms_factory.py
    - SegRescale:
        long_size: 512

    - SegRandomFlip:
        prob: 0.5

    - SegColorJitter:
        brightness: 0.5
        contrast: 0.5
        saturation: 0.5

    - SegRandomRescale:
        scales: [ 0.5, 2.0 ]

    - SegPadShortToCropSize:
        crop_size: 512
        fill_mask: 21

    - SegCropImageAndMask:
        crop_size: 512
        mode: random

    - SegStandardize:
        max_value: 255

    - SegNormalize:
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]

    - SegConvertToTensor:
        mask_output_dtype: long



val_dataset_params:
  root: /data/pascal_voc_2012
  list_file: VOCdevkit/VOC2012/ImageSets/Segmentation/val.txt
  samples_sub_directory: VOCdevkit/VOC2012/JPEGImages
  targets_sub_directory: VOCdevkit/VOC2012/SegmentationClass
  cache_labels: False
  cache_images: False
  transforms:
    - SegRescale:
        long_size: 512

    - SegPadShortToCropSize:
        crop_size: 512
        fill_mask: 21

    - SegCropImageAndMask:
        crop_size: 512
        mode: center

    - SegStandardize:
        max_value: 255

    - SegNormalize:
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]

    - SegConvertToTensor:
        mask_output_dtype: long




train_dataloader_params:
  shuffle: True
  batch_size: 16
  num_workers: 8
  drop_last: True
  pin_memory: True

val_dataloader_params:
  batch_size: 16
  num_workers: 8
  drop_last: False
  pin_memory: True
