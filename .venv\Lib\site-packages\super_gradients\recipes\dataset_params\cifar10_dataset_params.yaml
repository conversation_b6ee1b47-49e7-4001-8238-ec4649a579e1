batch_size: 256 # batch size for trainset
val_batch_size: 512 # batch size for valset in DatasetInterface

# TODO: REMOVE ABOVE, HERE FOR COMPATIBILITY UNTIL WE REMOVE DATASET_INTERFACE

train_dataset_params:
  root: ./data/cifar10
  train: True
  transforms:
    - RandomCrop:
        size: 32
        padding: 4
    - RandomHorizontalFlip
    - ToTensor
    - Normalize:
        mean:
          - 0.4914
          - 0.4822
          - 0.4465
        std:
          - 0.2023
          - 0.1994
          - 0.2010
  target_transform: null
  download: True

train_dataloader_params:
  shuffle: True
  batch_size: 256
  num_workers: 8
  drop_last: False
  pin_memory: True

val_dataset_params:
  root: ./data/cifar10
  train: False
  transforms:
    - Resize:
        size: 32
    - ToTensor
    - Normalize:
        mean:
          - 0.4914
          - 0.4822
          - 0.4465
        std:
          - 0.2023
          - 0.1994
          - 0.2010
  target_transform: null
  download: True

val_dataloader_params:
  batch_size: 512
  num_workers: 8
  drop_last: False
  pin_memory: True
