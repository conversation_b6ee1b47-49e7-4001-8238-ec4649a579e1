#!/usr/bin/env python3
"""
YOLO-NAS Inference Script for Barcode Detection

This script performs inference on images using trained YOLO-NAS models.
It can process single images, directories of images, or video files.

Usage:
    python scripts/inference.py --model models/best/model.pth --input image.jpg
    python scripts/inference.py --model models/best/model.pth --input images/ --output results/
    python scripts/inference.py --model models/best/model.pth --input video.mp4 --output output.mp4
"""

import argparse
import os
import sys
import cv2
import torch
import numpy as np
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import logging

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from super_gradients.training import models
from super_gradients.training.models.detection_models.pp_yolo_e import PPYoloEPostPredictionCallback

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BarcodeDetector:
    """YOLO-NAS based barcode detector."""
    
    def __init__(self, model_path, device='auto', confidence_threshold=0.5, nms_threshold=0.5):
        self.device = self._setup_device(device)
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        self.classes = ['Barcode', 'QR Code']  # Default classes
        
        # Load model
        self.model = self._load_model(model_path)
        
        # Setup post-processing
        self.post_prediction_callback = PPYoloEPostPredictionCallback(
            score_threshold=confidence_threshold,
            nms_top_k=1000,
            max_predictions=300,
            nms_threshold=nms_threshold
        )
        
        logger.info(f"BarcodeDetector initialized on {self.device}")
    
    def _setup_device(self, device):
        """Setup computation device."""
        if device == 'auto':
            return 'cuda' if torch.cuda.is_available() else 'cpu'
        return device
    
    def _load_model(self, model_path):
        """Load trained model."""
        try:
            model = models.get('yolo_nas_s', num_classes=len(self.classes), pretrained_weights=None)
            
            if model_path.endswith('.pth'):
                checkpoint = torch.load(model_path, map_location=self.device)
                model.load_state_dict(checkpoint)
            else:
                # Load from checkpoint directory
                from super_gradients.training import Trainer
                trainer = Trainer(experiment_name="inference", ckpt_root_dir=str(Path(model_path).parent))
                model = trainer.load_model(model_path)
            
            model.to(self.device)
            model.eval()
            
            logger.info(f"Model loaded from {model_path}")
            return model
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            sys.exit(1)
    
    def preprocess_image(self, image):
        """Preprocess image for inference."""
        if isinstance(image, str):
            image = cv2.imread(image)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        elif isinstance(image, np.ndarray) and len(image.shape) == 3:
            if image.shape[2] == 3:  # BGR to RGB
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Convert to tensor and normalize
        image_tensor = torch.from_numpy(image).permute(2, 0, 1).float() / 255.0
        image_tensor = image_tensor.unsqueeze(0).to(self.device)
        
        return image_tensor, image
    
    def detect(self, image_path_or_array):
        """Detect barcodes in image."""
        image_tensor, original_image = self.preprocess_image(image_path_or_array)
        
        with torch.no_grad():
            predictions = self.model(image_tensor)
        
        # Post-process predictions
        processed_predictions = self.post_prediction_callback(predictions)
        
        # Extract detection results
        detections = []
        if len(processed_predictions) > 0 and len(processed_predictions[0]) > 0:
            pred = processed_predictions[0]
            
            # pred format: [x1, y1, x2, y2, confidence, class_id]
            for detection in pred:
                x1, y1, x2, y2, confidence, class_id = detection[:6]
                
                if confidence >= self.confidence_threshold:
                    detections.append({
                        'bbox': [float(x1), float(y1), float(x2), float(y2)],
                        'confidence': float(confidence),
                        'class_id': int(class_id),
                        'class_name': self.classes[int(class_id)] if int(class_id) < len(self.classes) else 'Unknown'
                    })
        
        return detections, original_image
    
    def visualize_detections(self, image, detections, save_path=None):
        """Visualize detections on image."""
        if isinstance(image, np.ndarray):
            image = Image.fromarray(image)
        
        draw = ImageDraw.Draw(image)
        
        # Colors for different classes
        colors = ['red', 'blue', 'green', 'yellow', 'purple']
        
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            confidence = detection['confidence']
            class_name = detection['class_name']
            class_id = detection['class_id']
            
            # Choose color
            color = colors[class_id % len(colors)]
            
            # Draw bounding box
            draw.rectangle([x1, y1, x2, y2], outline=color, width=3)
            
            # Draw label
            label = f"{class_name}: {confidence:.2f}"
            bbox = draw.textbbox((x1, y1), label, font=font)
            draw.rectangle(bbox, fill=color)
            draw.text((x1, y1), label, fill='white', font=font)
        
        if save_path:
            image.save(save_path)
            logger.info(f"Visualization saved to {save_path}")
        
        return image


def process_single_image(detector, input_path, output_path=None):
    """Process a single image."""
    logger.info(f"Processing image: {input_path}")
    
    detections, image = detector.detect(input_path)
    
    logger.info(f"Found {len(detections)} detections")
    for i, detection in enumerate(detections):
        logger.info(f"  Detection {i+1}: {detection['class_name']} ({detection['confidence']:.3f})")
    
    if output_path:
        detector.visualize_detections(image, detections, output_path)
    
    return detections


def process_directory(detector, input_dir, output_dir):
    """Process all images in a directory."""
    input_dir = Path(input_dir)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Supported image extensions
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    
    image_files = [f for f in input_dir.iterdir() 
                   if f.suffix.lower() in image_extensions]
    
    logger.info(f"Processing {len(image_files)} images from {input_dir}")
    
    all_detections = {}
    
    for image_file in image_files:
        output_file = output_dir / f"{image_file.stem}_detected{image_file.suffix}"
        detections = process_single_image(detector, str(image_file), str(output_file))
        all_detections[str(image_file)] = detections
    
    # Save detection summary
    summary_path = output_dir / 'detection_summary.txt'
    with open(summary_path, 'w') as f:
        f.write("Barcode Detection Summary\n")
        f.write("=" * 50 + "\n\n")
        
        total_detections = 0
        for image_path, detections in all_detections.items():
            f.write(f"Image: {Path(image_path).name}\n")
            f.write(f"Detections: {len(detections)}\n")
            
            for i, detection in enumerate(detections):
                f.write(f"  {i+1}. {detection['class_name']} ({detection['confidence']:.3f})\n")
            
            f.write("\n")
            total_detections += len(detections)
        
        f.write(f"Total detections across all images: {total_detections}\n")
    
    logger.info(f"Detection summary saved to {summary_path}")
    return all_detections


def process_video(detector, input_path, output_path):
    """Process video file."""
    logger.info(f"Processing video: {input_path}")
    
    cap = cv2.VideoCapture(input_path)
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Setup video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    total_detections = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Detect barcodes
        detections, _ = detector.detect(frame)
        total_detections += len(detections)
        
        # Draw detections on frame
        for detection in detections:
            x1, y1, x2, y2 = map(int, detection['bbox'])
            confidence = detection['confidence']
            class_name = detection['class_name']
            
            # Draw bounding box
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # Draw label
            label = f"{class_name}: {confidence:.2f}"
            cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        out.write(frame)
        frame_count += 1
        
        if frame_count % 30 == 0:
            logger.info(f"Processed {frame_count}/{total_frames} frames")
    
    cap.release()
    out.release()
    
    logger.info(f"Video processing completed")
    logger.info(f"Total detections: {total_detections}")
    logger.info(f"Output saved to: {output_path}")


def main():
    parser = argparse.ArgumentParser(description='YOLO-NAS Barcode Detection Inference')
    parser.add_argument('--model', type=str, required=True,
                        help='Path to trained model')
    parser.add_argument('--input', type=str, required=True,
                        help='Input image, directory, or video file')
    parser.add_argument('--output', type=str,
                        help='Output path (file for single image/video, directory for batch)')
    parser.add_argument('--confidence', type=float, default=0.5,
                        help='Confidence threshold for detections')
    parser.add_argument('--nms-threshold', type=float, default=0.5,
                        help='NMS threshold for post-processing')
    parser.add_argument('--device', type=str, default='auto',
                        help='Device to use (auto, cpu, cuda)')
    
    args = parser.parse_args()
    
    # Initialize detector
    detector = BarcodeDetector(
        model_path=args.model,
        device=args.device,
        confidence_threshold=args.confidence,
        nms_threshold=args.nms_threshold
    )
    
    input_path = Path(args.input)
    
    if input_path.is_file():
        # Single file processing
        if input_path.suffix.lower() in {'.mp4', '.avi', '.mov', '.mkv'}:
            # Video file
            output_path = args.output or f"{input_path.stem}_detected{input_path.suffix}"
            process_video(detector, str(input_path), output_path)
        else:
            # Image file
            output_path = args.output or f"{input_path.stem}_detected{input_path.suffix}"
            process_single_image(detector, str(input_path), output_path)
    
    elif input_path.is_dir():
        # Directory processing
        output_dir = args.output or f"{input_path.name}_detected"
        process_directory(detector, str(input_path), output_dir)
    
    else:
        logger.error(f"Input path does not exist: {input_path}")
        sys.exit(1)


if __name__ == "__main__":
    main()
