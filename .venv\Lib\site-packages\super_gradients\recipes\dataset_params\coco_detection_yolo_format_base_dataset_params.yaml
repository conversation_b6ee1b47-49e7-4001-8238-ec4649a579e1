
train_dataset_params:
  data_dir: /data/coco # TO FILL: Where the data is stored.
  images_dir: images/train2017 # TO FILL: Local path to directory that includes all the images. Path relative to `data_dir`. Can be the same as `labels_dir`.
  labels_dir: labels/train2017 # TO FILL: Local path to directory that includes all the labels. Path relative to `data_dir`. Can be the same as `images_dir`.
  classes: [ person, bicycle, car, motorcycle, airplane, bus, train, truck, boat, traffic light, fire hydrant, stop sign,
             parking meter, bench, bird, cat, dog, horse, sheep, cow, elephant, bear, zebra, giraffe, backpack, umbrella, handbag,
             tie, suitcase, frisbee, skis, snowboard, sports ball, kite, baseball bat, baseball glove, skateboard, surfboard,
             tennis racket, bottle, wine glass, cup, fork, knife, spoon, bowl, banana, apple, sandwich, orange, broccoli, carrot,
             hot dog, pizza, donut, cake, chair, couch, potted plant, bed, dining table, toilet, tv, laptop, mouse, remote,
             keyboard, cell phone, microwave, oven, toaster, sink, refrigerator, book, clock, vase, scissors, teddy bear,
             hair drier, toothbrush] # TO FILL: List of classes used in your dataset.
  input_dim: [640, 640]
  cache_annotations: True
  ignore_empty_annotations: True
  transforms:
    - DetectionMosaic:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
        prob: 1.
    - DetectionRandomAffine:
        degrees: 10.                  # rotation degrees, randomly sampled from [-degrees, degrees]
        translate: 0.1                # image translation fraction
        scales: [ 0.1, 2 ]              # random rescale range (keeps size by padding/cropping) after mosaic transform.
        shear: 2.0                    # shear degrees, randomly sampled from [-degrees, degrees]
        target_size: ${dataset_params.train_dataset_params.input_dim}
        filter_box_candidates: True   # whether to filter out transformed bboxes by edge size, area ratio, and aspect ratio.
        wh_thr: 2                     # edge size threshold when filter_box_candidates = True (pixels)
        area_thr: 0.1                 # threshold for area ratio between original image and the transformed one, when when filter_box_candidates = True
        ar_thr: 20                    # aspect ratio threshold when filter_box_candidates = True
    - DetectionMixup:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
        mixup_scale: [ 0.5, 1.5 ]         # random rescale range for the additional sample in mixup
        prob: 1.0                       # probability to apply per-sample mixup
        flip_prob: 0.5                  # probability to apply horizontal flip
    - DetectionHSV:
        prob: 1.0                       # probability to apply HSV transform
        hgain: 5                        # HSV transform hue gain (randomly sampled from [-hgain, hgain])
        sgain: 30                       # HSV transform saturation gain (randomly sampled from [-sgain, sgain])
        vgain: 30                       # HSV transform value gain (randomly sampled from [-vgain, vgain])
    - DetectionHorizontalFlip:
        prob: 0.5                       # probability to apply horizontal flip
    - DetectionPaddedRescale:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
    - DetectionTargetsFormatTransform:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
        output_format: LABEL_CXCYWH
  class_inclusion_list:
  max_num_samples:

train_dataloader_params:
  batch_size: 25
  num_workers: 8
  shuffle: True
  drop_last: True
  pin_memory: True
  collate_fn: DetectionCollateFN

val_dataset_params:
  data_dir: /data/coco # TO FILL: Where the data is stored.
  images_dir: images/val2017 # TO FILL: Local path to directory that includes all the images. Path relative to `data_dir`. Can be the same as `labels_dir`.
  labels_dir: labels/val2017 # TO FILL: Local path to directory that includes all the labels. Path relative to `data_dir`. Can be the same as `images_dir`.
  classes: [ person, bicycle, car, motorcycle, airplane, bus, train, truck, boat, traffic light, fire hydrant, stop sign,
             parking meter, bench, bird, cat, dog, horse, sheep, cow, elephant, bear, zebra, giraffe, backpack, umbrella, handbag,
             tie, suitcase, frisbee, skis, snowboard, sports ball, kite, baseball bat, baseball glove, skateboard, surfboard,
             tennis racket, bottle, wine glass, cup, fork, knife, spoon, bowl, banana, apple, sandwich, orange, broccoli, carrot,
             hot dog, pizza, donut, cake, chair, couch, potted plant, bed, dining table, toilet, tv, laptop, mouse, remote,
             keyboard, cell phone, microwave, oven, toaster, sink, refrigerator, book, clock, vase, scissors, teddy bear,
             hair drier, toothbrush] # TO FILL: List of classes used in your dataset.
  input_dim: [640, 640]
  cache_annotations: True
  ignore_empty_annotations: True
  transforms:
  - DetectionPaddedRescale:
      input_dim: ${dataset_params.val_dataset_params.input_dim}
  - DetectionTargetsFormatTransform:
      input_dim: ${dataset_params.val_dataset_params.input_dim}
      output_format: LABEL_CXCYWH
  class_inclusion_list:
  max_num_samples:

val_dataloader_params:
  batch_size: 25
  num_workers: 8
  drop_last: False
  pin_memory: True
  collate_fn: DetectionCollateFN

_convert_: all
