# Training Guide

This guide covers everything you need to know about training YOLO-NAS models for barcode detection.

## Quick Start

1. **Prepare your dataset:**
   ```bash
   python scripts/utils/convert_coco_to_yolo.py --input Barcodes.v5i.coco --output data/processed
   ```

2. **Start training:**
   ```bash
   python scripts/train.py --config configs/training/yolo_nas_default.yaml
   ```

3. **Monitor training:**
   ```bash
   tensorboard --logdir logs/
   ```

## Model Selection

YOLO-NAS offers three model sizes:

| Model | Parameters | Speed | Accuracy | GPU Memory |
|-------|------------|-------|----------|------------|
| yolo_nas_s | 12M | Fast | Good | 4-6GB |
| yolo_nas_m | 33M | Medium | Better | 6-8GB |
| yolo_nas_l | 44M | Slow | Best | 8-12GB |

Choose based on your requirements:
- **yolo_nas_s**: For real-time applications or limited GPU memory
- **yolo_nas_m**: Balanced performance and accuracy
- **yolo_nas_l**: Maximum accuracy for offline processing

## Training Configuration

### Basic Configuration

Edit `configs/training/yolo_nas_default.yaml`:

```yaml
# Model selection
model:
  name: "yolo_nas_s"  # or yolo_nas_m, yolo_nas_l
  pretrained_weights: "coco"

# Training parameters
training:
  epochs: 100
  batch_size: 16
  learning_rate: 0.001
```

### Advanced Configuration

#### Learning Rate Scheduling

```yaml
training:
  lr_scheduler:
    type: "cosine"
    warmup_epochs: 3
    warmup_lr: 0.0001
    final_lr_ratio: 0.1
```

#### Data Augmentation

```yaml
dataset:
  augmentation:
    enabled: true
    mosaic_prob: 0.5
    mixup_prob: 0.15
    hsv_prob: 0.5
    flip_prob: 0.5
    rotation_prob: 0.2
    scale_range: [0.8, 1.2]
```

#### Mixed Precision Training

```yaml
training:
  mixed_precision: true  # Reduces memory usage and speeds up training
```

## Command Line Options

### Basic Training

```bash
# Use default configuration
python scripts/train.py

# Specify configuration file
python scripts/train.py --config configs/training/yolo_nas_default.yaml

# Override specific parameters
python scripts/train.py --model yolo_nas_m --epochs 150 --batch-size 8
```

### Advanced Options

```bash
# Custom experiment name
python scripts/train.py --experiment-name "barcode_detection_v1"

# Custom learning rate
python scripts/train.py --lr 0.0005

# Resume from checkpoint
python scripts/train.py --resume models/checkpoints/experiment/ckpt_latest.pth
```

## Monitoring Training

### TensorBoard

Start TensorBoard to monitor training progress:

```bash
tensorboard --logdir logs/
```

Open http://localhost:6006 in your browser to view:
- Loss curves
- Learning rate schedule
- Validation metrics
- Sample predictions

### Training Logs

Check training logs:
```bash
tail -f logs/training.log
```

### Key Metrics to Watch

1. **Training Loss**: Should decrease steadily
2. **Validation mAP@0.50**: Should increase over time
3. **Learning Rate**: Should follow the schedule
4. **GPU Memory Usage**: Should be stable

## Hyperparameter Tuning

### Batch Size

- **Large batch size (16-32)**: Faster training, more stable gradients
- **Small batch size (4-8)**: Less memory usage, may need more epochs

```bash
# For limited GPU memory
python scripts/train.py --batch-size 4

# For high-end GPUs
python scripts/train.py --batch-size 32
```

### Learning Rate

- **High LR (0.01)**: Faster convergence, risk of instability
- **Low LR (0.0001)**: Stable training, slower convergence

```bash
# Conservative approach
python scripts/train.py --lr 0.0001

# Aggressive approach
python scripts/train.py --lr 0.01
```

### Number of Epochs

- **Few epochs (50-100)**: Quick experiments
- **Many epochs (200-500)**: Maximum performance

Monitor validation metrics to avoid overfitting.

## Training Strategies

### Transfer Learning

Start with COCO pre-trained weights:

```yaml
model:
  pretrained_weights: "coco"  # Recommended for most cases
```

### Progressive Training

1. **Phase 1**: Train with small images (416x416) for 50 epochs
2. **Phase 2**: Increase to full resolution (640x640) for remaining epochs

### Data Augmentation Strategy

For barcode detection, effective augmentations include:
- **Horizontal flipping**: Barcodes can appear in any orientation
- **Rotation**: Small rotations (±15°) for real-world scenarios
- **HSV adjustments**: Handle different lighting conditions
- **Mosaic**: Improve detection of small barcodes

## Common Training Issues

### Overfitting

**Symptoms:**
- Training loss decreases but validation loss increases
- High training mAP but low validation mAP

**Solutions:**
- Increase data augmentation
- Add more training data
- Reduce model complexity
- Use early stopping

### Underfitting

**Symptoms:**
- Both training and validation loss plateau at high values
- Low mAP on both training and validation sets

**Solutions:**
- Increase model complexity (yolo_nas_s → yolo_nas_m)
- Increase learning rate
- Train for more epochs
- Reduce data augmentation

### Memory Issues

**Out of Memory Error:**
```bash
# Reduce batch size
python scripts/train.py --batch-size 4

# Enable gradient accumulation
python scripts/train.py --batch-size 4 --accumulate-grad-batches 4
```

### Slow Training

**Solutions:**
- Use mixed precision training
- Increase batch size if memory allows
- Use faster data loading (more workers)
- Ensure data is on SSD

## Best Practices

### 1. Start Small
- Begin with yolo_nas_s and small batch size
- Verify everything works before scaling up

### 2. Monitor Early
- Watch first few epochs closely
- Stop if loss doesn't decrease

### 3. Save Checkpoints
- Training saves checkpoints automatically
- Keep multiple checkpoints for comparison

### 4. Validate Regularly
- Default validation every epoch
- Early stopping prevents overfitting

### 5. Document Experiments
- Use descriptive experiment names
- Keep notes on configuration changes

## Multi-GPU Training

For multiple GPUs:

```yaml
hardware:
  multi_gpu: true
  sync_bn: true
```

```bash
# Use all available GPUs
python scripts/train.py --config configs/training/yolo_nas_default.yaml
```

## Training Time Estimates

Approximate training times for 100 epochs:

| Model | Batch Size | GPU | Time |
|-------|------------|-----|------|
| yolo_nas_s | 16 | RTX 3070 | 2-3 hours |
| yolo_nas_m | 16 | RTX 3070 | 4-5 hours |
| yolo_nas_l | 8 | RTX 3070 | 6-8 hours |
| yolo_nas_s | 32 | RTX 4090 | 1-2 hours |

## After Training

### Model Evaluation

```bash
python scripts/evaluate.py --model models/best/experiment_best.pth --data data/processed/test
```

### Inference Testing

```bash
python scripts/inference.py --model models/best/experiment_best.pth --input test_image.jpg
```

### Model Export

Export for deployment:

```bash
# Export to ONNX
python scripts/export.py --model models/best/experiment_best.pth --format onnx

# Export to TensorRT
python scripts/export.py --model models/best/experiment_best.pth --format tensorrt
```

## Troubleshooting

### Training Doesn't Start
- Check dataset paths in configuration
- Verify CUDA availability
- Check disk space

### Poor Performance
- Increase training epochs
- Adjust learning rate
- Add more training data
- Review data quality

### Training Crashes
- Reduce batch size
- Check GPU memory
- Verify dataset integrity

For more detailed troubleshooting, see `docs/troubleshooting.md`.
