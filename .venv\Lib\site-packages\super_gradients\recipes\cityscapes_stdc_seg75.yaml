#  STDC segmentation training example with Cityscapes dataset.
#  Reproduction and refinement of paper: Rethinking BiSeNet For Real-time Semantic Segmentation.
#

# Instructions:
#   0. Make sure that the data is stored in dataset_params.dataset_dir or add "dataset_params.data_dir=<PATH-TO-DATASET>" at the end of the command below (feel free to check ReadMe)
#   1. Move to the project root (where you will find the ReadMe and src folder)
#   2. Run the command:
#       STDC1-Seg75: python -m super_gradients.train_from_recipe --config-name=cityscapes_stdc_seg75
#       STDC2-Seg75: python -m super_gradients.train_from_recipe --config-name=cityscapes_stdc_seg75 architecture=stdc2_seg
# Note: add "external_checkpoint_path=<stdc1-backbone-pretrained-path>" to use pretrained backbone
#
#
#
#  Validation mIoU - Cityscapes, training time:
#      STDC1-Seg75:    input-size: [768, 1536]     mIoU: 76.87     4 X RTX A5000, 29 H, early stopped after 711 epochs
#      STDC2-Seg75:    input-size: [768, 1536]     mIoU: 78.93     2 X RTX A5000, 29 H, early stopped after 530 epochs
#
#  Official git repo:
#      https://github.com/<PERSON>Fan01/STDC-Seg
#  Paper:
#      https://arxiv.org/abs/2104.13188
#
#  Pretrained checkpoints:
#      Backbones- downloaded from the author's official repo.
#       https://deci-pretrained-models.s3.amazonaws.com/stdc_backbones/stdc1_imagenet_pretrained.pth
#       https://deci-pretrained-models.s3.amazonaws.com/stdc_backbones/stdc2_imagenet_pretrained.pth
#
#      Logs, tensorboards and network checkpoints:
#       https://deci-pretrained-models.s3.amazonaws.com/stdc1_seg75_cityscapes/
#       https://deci-pretrained-models.s3.amazonaws.com/stdc2_seg75_cityscapes/
#
#
#  Learning rate and batch size parameters, using 4 GeForce RTX 2080 Ti with DDP:
#      STDC1-Seg75:    input-size: [768, 1536]     initial_lr: 0.005   batch-size: 4 * 4gpus = 16
#      STDC2-Seg75:    input-size: [768, 1536]     initial_lr: 0.005   batch-size: 8 * 2gpus = 16
#
#  Comments:
#      * Pretrained backbones were used.
#      * Results with Deci code are higher than original implementation, mostly thanks to changes in Detail loss and
#          module, different auxiliary feature maps and different loss weights.

defaults:
  - training_hyperparams: cityscapes_default_train_params
  - dataset_params: cityscapes_stdc_seg75_dataset_params
  - checkpoint_params: default_checkpoint_params
  - _self_
  - variable_setup

train_dataloader: cityscapes_train
val_dataloader: cityscapes_val


architecture: stdc1_seg

arch_params:
  num_classes: 19
  use_aux_heads: True

checkpoint_params:
  checkpoint_path:
  load_backbone: True
  load_weights_only: True
  strict_load: no_key_matching

training_hyperparams:
  initial_lr:
    cp: 0.005
    default: 0.05

  sync_bn: True

  loss:
    STDCLoss:
      num_classes: 19
      ignore_index: 19
      mining_percent: 0.0625 # mining percentage is 1/16 of pixels following original implementation.
      weights: [ 1., 0.6, 0.4, 1. ]

multi_gpu: DDP
num_gpus: 4

experiment_name: ${architecture}75_cityscapes
