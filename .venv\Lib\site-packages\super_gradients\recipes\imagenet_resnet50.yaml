#  ResNet50 Imagenet classification training:
#  This example trains with batch_size = 64 * 4 GPUs, total 256.
#  Training time on 4 x GeForce RTX A5000 is 15min / epoch.
#  Reach => 79.47 Top1 accuracy.
#
#  Log and tensorboard at s3://deci-pretrained-models/ResNet50_ImageNet/average_model.pth

# Instructions:
#   0. Make sure that the data is stored in dataset_params.dataset_dir or add "dataset_params.data_dir=<PATH-TO-DATASET>" at the end of the command below (feel free to check ReadMe)
#   1. Move to the project root (where you will find the ReadMe and src folder)
#   2. Run the command:
#       python -m super_gradients.train_from_recipe --config-name=imagenet_resnet50


defaults:
  - training_hyperparams: imagenet_resnet50_train_params
  - dataset_params: imagenet_resnet50_dataset_params
  - arch_params: resnet50_arch_params
  - checkpoint_params: default_checkpoint_params
  - _self_
  - variable_setup

arch_params:
  droppath_prob: 0.05

train_dataloader: imagenet_train
val_dataloader: imagenet_val


resume: False
training_hyperparams:
  resume: ${resume}

experiment_name: resnet50_imagenet



multi_gpu: DDP
num_gpus: 4

architecture: resnet50
