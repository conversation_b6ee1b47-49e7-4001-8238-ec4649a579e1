# YOLO-NAS Barcode Detection Training Project

This project provides a complete setup for training YOLO-NAS models on custom datasets, specifically configured for barcode detection benchmarking.

## Project Structure

```
barcode_detection_benchmark/
├── .venv/                      # UV virtual environment
├── data/                       # Dataset storage
│   ├── raw/                   # Original COCO format data
│   ├── processed/             # Processed YOLO format data
│   └── splits/                # Train/val/test splits
├── models/                     # Model storage
│   ├── pretrained/            # Pre-trained models
│   ├── checkpoints/           # Training checkpoints
│   └── best/                  # Best trained models
├── configs/                    # Configuration files
│   ├── training/              # Training configurations
│   ├── dataset/               # Dataset configurations
│   └── hyperparameters/       # Hyperparameter configs
├── scripts/                    # Training and utility scripts
│   ├── train.py              # Main training script
│   ├── evaluate.py           # Model evaluation
│   ├── inference.py          # Inference script
│   └── utils/                # Utility functions
├── notebooks/                  # Jupyter notebooks for experimentation
├── logs/                      # Training logs and tensorboard
├── results/                   # Training results and metrics
├── docs/                      # Documentation
├── requirements.txt           # Dependencies
├── pyproject.toml            # UV project configuration
└── README.md                 # This file
```

## Quick Start

**🚀 Super Simple Setup - No Dataset Conversion Needed!**

Your COCO format dataset works directly with YOLO-NAS!

1. **One-Command Setup and Training**:

   ```bash
   python quick_start.py
   ```

   This will:

   - Install all dependencies
   - Verify your environment
   - Start training immediately with your COCO dataset

2. **Alternative: Manual Setup**:

   ```bash
   # Create and activate virtual environment
   uv venv
   .venv\Scripts\activate  # Windows
   # source .venv/bin/activate  # Linux/Mac

   # Install dependencies
   python install_dependencies.py

   # Start training
   python scripts/train.py --config configs/training/yolo_nas_default.yaml
   ```

3. **Monitor Training**:
   ```bash
   tensorboard --logdir logs/
   ```

## Environment Requirements

- Python 3.8+
- CUDA 11.8+ (for GPU training)
- 8GB+ GPU memory recommended
- UV package manager

## Key Features

- ✅ Complete YOLO-NAS training pipeline
- ✅ **Direct COCO format support** - No conversion needed!
- ✅ One-command setup and training
- ✅ Configurable training parameters
- ✅ TensorBoard integration
- ✅ Model evaluation and metrics
- ✅ Inference utilities
- ✅ CUDA optimization
- ✅ Python 3.11 compatible

## Training Workflow

1. **Data Ready**: Your COCO format dataset works directly!
2. **Model Selection**: Choose from yolo_nas_s, yolo_nas_m, or yolo_nas_l
3. **Configuration**: Set hyperparameters and training parameters
4. **Training**: Run training with automatic checkpointing
5. **Evaluation**: Assess model performance on test set
6. **Inference**: Use trained model for predictions

## Documentation

- [Setup Guide](docs/setup.md)
- [Training Guide](docs/training.md)
- [Configuration Reference](docs/configuration.md)
- [Troubleshooting](docs/troubleshooting.md)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
