# YOLO-NAS Barcode Detection Training Project

This project provides a complete setup for training YOLO-NAS models on custom datasets, specifically configured for barcode detection benchmarking.

## Project Structure

```
barcode_detection_benchmark/
├── .venv/                      # UV virtual environment
├── data/                       # Dataset storage
│   ├── raw/                   # Original COCO format data
│   ├── processed/             # Processed YOLO format data
│   └── splits/                # Train/val/test splits
├── models/                     # Model storage
│   ├── pretrained/            # Pre-trained models
│   ├── checkpoints/           # Training checkpoints
│   └── best/                  # Best trained models
├── configs/                    # Configuration files
│   ├── training/              # Training configurations
│   ├── dataset/               # Dataset configurations
│   └── hyperparameters/       # Hyperparameter configs
├── scripts/                    # Training and utility scripts
│   ├── train.py              # Main training script
│   ├── evaluate.py           # Model evaluation
│   ├── inference.py          # Inference script
│   └── utils/                # Utility functions
├── notebooks/                  # Jupyter notebooks for experimentation
├── logs/                      # Training logs and tensorboard
├── results/                   # Training results and metrics
├── docs/                      # Documentation
├── requirements.txt           # Dependencies
├── pyproject.toml            # UV project configuration
└── README.md                 # This file
```

## Quick Start

1. **Environment Setup**:
   ```bash
   # Install UV if not already installed
   curl -LsSf https://astral.sh/uv/install.sh | sh
   
   # Create and activate virtual environment
   uv venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   
   # Install dependencies
   uv pip install -r requirements.txt
   ```

2. **Verify CUDA Setup**:
   ```bash
   python scripts/utils/check_cuda.py
   ```

3. **Prepare Your Dataset**:
   - Place your COCO format dataset in `data/raw/`
   - Run dataset conversion: `python scripts/utils/convert_coco_to_yolo.py`

4. **Start Training**:
   ```bash
   python scripts/train.py --config configs/training/yolo_nas_default.yaml
   ```

## Environment Requirements

- Python 3.8+
- CUDA 11.8+ (for GPU training)
- 8GB+ GPU memory recommended
- UV package manager

## Key Features

- ✅ Complete YOLO-NAS training pipeline
- ✅ COCO to YOLO format conversion
- ✅ Configurable training parameters
- ✅ TensorBoard integration
- ✅ Model evaluation and metrics
- ✅ Inference utilities
- ✅ CUDA optimization

## Training Workflow

1. **Data Preparation**: Convert COCO annotations to YOLO format
2. **Model Selection**: Choose from yolo_nas_s, yolo_nas_m, or yolo_nas_l
3. **Configuration**: Set hyperparameters and training parameters
4. **Training**: Run training with automatic checkpointing
5. **Evaluation**: Assess model performance on test set
6. **Inference**: Use trained model for predictions

## Documentation

- [Setup Guide](docs/setup.md)
- [Training Guide](docs/training.md)
- [Configuration Reference](docs/configuration.md)
- [Troubleshooting](docs/troubleshooting.md)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
