resume: False # Whether to continue training from ckpt from the latest run, within the same experiment name.
run_id:       # ID of run to resume from the same experiment.
resume_path:  # Explicit checkpoint path (.pth file) to use to resume training.

resume_from_remote_sg_logger: False # bool (default=False), When true, ckpt_name (checkpoint filename
#        to resume i.e ckpt_latest.pth bydefault) will be downloaded into the experiment checkpoints directory
#        prior to loading weights, then training is resumed from that checkpoint. The source is unique to
#        every logger, and currently supported for WandB loggers only.
#
#        IMPORTANT: Only works for experiments that were ran with sg_logger_params.save_checkpoints_remote=True.
#        IMPORTANT: For WandB loggers, one must also pass the run id through the wandb_id arg in sg_logger_params.

ckpt_name: ckpt_latest.pth  # The checkpoint (.pth file) filename in CKPT_ROOT_DIR/EXPERIMENT_NAME/ to use when resume=True and resume_path=None

lr_mode: # Union[str, Mapping]
         # when str: Learning rate scheduling policy, one of ["StepLRScheduler", "PolyLRScheduler", "CosineLRScheduler", "ExponentialLRScheduler", "FunctionLRScheduler"]
         # when Mapping: refers to a torch.optim.lr_scheduler._LRScheduler, following the below API: lr_mode = {LR_SCHEDULER_CLASS_NAME: {**LR_SCHEDULER_KWARGS, "phase": XXX, "metric_name": XXX)

lr_schedule_function: # Learning rate scheduling function to be used when `lr_mode` is 'FunctionLRScheduler'.
lr_warmup_epochs: 0 # number of epochs for learning rate warm up - see https://arxiv.org/pdf/1706.02677.pdf (Section 2.2).
lr_warmup_steps: 0  # number of warmup steps (Used when warmup_mode=LinearBatchLRWarmup)
lr_cooldown_epochs: 0 # epochs to cooldown LR (i.e the last epoch from scheduling view point=max_epochs-cooldown)
warmup_initial_lr: # Initial lr for LinearEpochLRWarmup/LinearBatchLRWarmup. When none is given, initial_lr/(warmup_epochs+1) will be used.
step_lr_update_freq: # (float) update frequency in epoch units for computing lr_updates when lr_mode=`StepLRScheduler`.
cosine_final_lr_ratio: 0.01 # final learning rate ratio (only relevant when `lr_mode`='CosineLRScheduler')
warmup_mode: LinearEpochLRWarmup # learning rate warmup scheme, currently ['LinearEpochLRWarmup', 'LinearEpochLRWarmup', 'LinearBatchLRWarmup'] are supported

lr_updates:
  _target_: super_gradients.training.utils.utils.empty_list # This is a workaround to instantiate a list using _target_. If we would instantiate as "lr_updates: []",
                                                            # we would get an error every time we would want to overwrite lr_updates with a numpy array.

pre_prediction_callback: # callback modifying images and targets right before forward pass.

optimizer: SGD # Optimization algorithm. One of ['Adam','SGD','RMSProp'] corresponding to the torch.optim optimizers
optimizer_params: {} # when `optimizer` is one of ['Adam','SGD','RMSProp'], it will be initialized with optimizer_params.
load_opt_params: True # Whether to load the optimizers parameters as well when loading a model's checkpoint
zero_weight_decay_on_bias_and_bn: False # whether to apply weight decay on batch normalization parameters or not


loss: # Loss function for training (str as one of SuperGradient's built in options, or torch.nn.module)
criterion_params: {} # when `loss` is one of SuperGradient's built in options, it will be initialized with criterion_params.


ema: False # whether to use Model Exponential Moving Average
ema_params: # parameters for the ema model.
  decay: 0.9999
  decay_type: exp
  beta: 15


train_metrics_list: [] # Metrics to log during training. For more information on torchmetrics see https://torchmetrics.rtfd.io/en/latest/.
valid_metrics_list: [] # Metrics to log during validation. For more information on torchmetrics see https://torchmetrics.rtfd.io/en/latest/
metric_to_watch: Accuracy # will be the metric which the model checkpoint will be saved according to
greater_metric_to_watch_is_better: True # When choosing a model's checkpoint to be saved, the best achieved model is the one that maximizes the metric_to_watch when this parameter is set to True


launch_tensorboard: False # Whether to launch a TensorBoard process.
tensorboard_port: # port for tensorboard process
tb_files_user_prompt: False  # Asks User for Tensorboard Deletion Prompt
save_tensorboard_to_s3: False # whether to save tb to s3


precise_bn: False # Whether to use precise_bn calculation during the training.
precise_bn_batch_size: # the effective batch size we want to calculate the batchnorm on.
sync_bn: False # Whether to convert bn layers to synched bn (for accurate stats in DDP).


silent_mode: False  # Silents the Print outs


mixed_precision: False # Whether to use mixed precision or not.


save_ckpt_epoch_list: []  # indices where the ckpt will save automatically


average_best_models: True # If set, a snapshot dictionary file and the average model will be saved


dataset_statistics: False  # add a dataset statistical analysis and sample images to tensorboard


batch_accumulate: 1  # number of batches to accumulate before every backward pass


run_validation_freq: 1 # The frequency in which validation is performed during training.
run_test_freq: 1 # The frequency in which test is performed during training.


save_model: True # Whether to save the model checkpoints


seed: 42 # seed for reproducibility


phase_callbacks: [] # list of callbacks to be applied at specific phases.


log_installed_packages: True # when set, the list of all installed packages (and their versions) will be written to the tensorboard


clip_grad_norm : # Defines a maximal L2 norm of the gradients. Values which exceed the given value will be clipped

ckpt_best_name: ckpt_best.pth

max_train_batches:  # For debug- when not None- will break out of inner train loop
# (i.e iterating over train_loader) when reaching this number of batches.

max_valid_batches:  # For debug- when not None- will break out of inner valid loop
# (i.e iterating over valid_loader) when reaching this number of batches.

sg_logger: base_sg_logger
sg_logger_params:
  tb_files_user_prompt: False # Asks User for Tensorboard Deletion Prompt
  launch_tensorboard: False
  tensorboard_port:
  save_checkpoints_remote: False  # upload checkpoint files to s3
  save_tensorboard_remote: False  # upload tensorboard files to s3
  save_logs_remote: False  # upload log files to s3
  monitor_system: True  # Monitor and write to tensorboard the system statistics, such as CPU usage, GPU, ...

torch_compile: False        # Enable or disable use of torch.compile to optimize the model (Requires Pytorch 2.0)
torch_compile_loss: False   # Enable or disable use of torch.compile to optimize the loss  (Requires Pytorch 2.0)

# torch.compile options from https://pytorch.org/docs/stable/generated/torch.compile.html
torch_compile_options:
  mode: reduce-overhead # default / reduce-overhead / max-autotune
  fullgraph: False      # Whether it is ok to break model into several subgraphs
  dynamic: False        # Use dynamic shape tracing
  backend: inductor     # backend to be used
  options:              # A dictionary of options to pass to the backend.
  disable: False        # Turn torch.compile() into a no-op for testing

finetune: False  # Whether to freeze a fixed part of the model. Supported only for models that implement get_finetune_lr_dict.
                    # The model's class method get_finetune_lr_dict should return a dictionary, mapping lr to the
                    # unfrozen part of the network, in the same fashion as using initial_lr.

_convert_: all
