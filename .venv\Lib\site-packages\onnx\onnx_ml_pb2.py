# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: onnx/onnx-ml.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12onnx/onnx-ml.proto\x12\x04onnx\"\xdb\x05\n\x0e\x41ttributeProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x15\n\rref_attr_name\x18\x15 \x01(\t\x12\x12\n\ndoc_string\x18\r \x01(\t\x12\x30\n\x04type\x18\x14 \x01(\x0e\x32\".onnx.AttributeProto.AttributeType\x12\t\n\x01\x66\x18\x02 \x01(\x02\x12\t\n\x01i\x18\x03 \x01(\x03\x12\t\n\x01s\x18\x04 \x01(\x0c\x12\x1c\n\x01t\x18\x05 \x01(\x0b\x32\x11.onnx.TensorProto\x12\x1b\n\x01g\x18\x06 \x01(\x0b\x32\x10.onnx.GraphProto\x12.\n\rsparse_tensor\x18\x16 \x01(\x0b\x32\x17.onnx.SparseTensorProto\x12\x1b\n\x02tp\x18\x0e \x01(\x0b\x32\x0f.onnx.TypeProto\x12\x0e\n\x06\x66loats\x18\x07 \x03(\x02\x12\x0c\n\x04ints\x18\x08 \x03(\x03\x12\x0f\n\x07strings\x18\t \x03(\x0c\x12\"\n\x07tensors\x18\n \x03(\x0b\x32\x11.onnx.TensorProto\x12 \n\x06graphs\x18\x0b \x03(\x0b\x32\x10.onnx.GraphProto\x12/\n\x0esparse_tensors\x18\x17 \x03(\x0b\x32\x17.onnx.SparseTensorProto\x12$\n\x0btype_protos\x18\x0f \x03(\x0b\x32\x0f.onnx.TypeProto\"\xd9\x01\n\rAttributeType\x12\r\n\tUNDEFINED\x10\x00\x12\t\n\x05\x46LOAT\x10\x01\x12\x07\n\x03INT\x10\x02\x12\n\n\x06STRING\x10\x03\x12\n\n\x06TENSOR\x10\x04\x12\t\n\x05GRAPH\x10\x05\x12\x11\n\rSPARSE_TENSOR\x10\x0b\x12\x0e\n\nTYPE_PROTO\x10\r\x12\n\n\x06\x46LOATS\x10\x06\x12\x08\n\x04INTS\x10\x07\x12\x0b\n\x07STRINGS\x10\x08\x12\x0b\n\x07TENSORS\x10\t\x12\n\n\x06GRAPHS\x10\n\x12\x12\n\x0eSPARSE_TENSORS\x10\x0c\x12\x0f\n\x0bTYPE_PROTOS\x10\x0eJ\x04\x08\x0c\x10\rJ\x04\x08\x10\x10\x14R\x01v\"\x87\x01\n\x0eValueInfoProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x1d\n\x04type\x18\x02 \x01(\x0b\x32\x0f.onnx.TypeProto\x12\x12\n\ndoc_string\x18\x03 \x01(\t\x12\x34\n\x0emetadata_props\x18\x04 \x03(\x0b\x32\x1c.onnx.StringStringEntryProto\"\xa1\x02\n\tNodeProto\x12\r\n\x05input\x18\x01 \x03(\t\x12\x0e\n\x06output\x18\x02 \x03(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0f\n\x07op_type\x18\x04 \x01(\t\x12\x0e\n\x06\x64omain\x18\x07 \x01(\t\x12\x10\n\x08overload\x18\x08 \x01(\t\x12\'\n\tattribute\x18\x05 \x03(\x0b\x32\x14.onnx.AttributeProto\x12\x12\n\ndoc_string\x18\x06 \x01(\t\x12\x34\n\x0emetadata_props\x18\t \x03(\x0b\x32\x1c.onnx.StringStringEntryProto\x12\x41\n\x15\x64\x65vice_configurations\x18\n \x03(\x0b\x32\".onnx.NodeDeviceConfigurationProto\"2\n\x14IntIntListEntryProto\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\r\n\x05value\x18\x02 \x03(\x03\"\x80\x01\n\x1cNodeDeviceConfigurationProto\x12\x18\n\x10\x63onfiguration_id\x18\x01 \x01(\t\x12.\n\rsharding_spec\x18\x02 \x03(\x0b\x32\x17.onnx.ShardingSpecProto\x12\x16\n\x0epipeline_stage\x18\x03 \x01(\x05\"\xa3\x01\n\x11ShardingSpecProto\x12\x13\n\x0btensor_name\x18\x01 \x01(\t\x12\x0e\n\x06\x64\x65vice\x18\x02 \x03(\x03\x12=\n\x19index_to_device_group_map\x18\x03 \x03(\x0b\x32\x1a.onnx.IntIntListEntryProto\x12*\n\x0bsharded_dim\x18\x04 \x03(\x0b\x32\x15.onnx.ShardedDimProto\"U\n\x0fShardedDimProto\x12\x0c\n\x04\x61xis\x18\x01 \x01(\x03\x12\x34\n\x0fsimple_sharding\x18\x02 \x03(\x0b\x32\x1b.onnx.SimpleShardedDimProto\"\\\n\x15SimpleShardedDimProto\x12\x13\n\tdim_value\x18\x01 \x01(\x03H\x00\x12\x13\n\tdim_param\x18\x02 \x01(\tH\x00\x12\x12\n\nnum_shards\x18\x03 \x01(\x03\x42\x05\n\x03\x64im\"\xd6\x01\n\x11TrainingInfoProto\x12(\n\x0einitialization\x18\x01 \x01(\x0b\x32\x10.onnx.GraphProto\x12#\n\talgorithm\x18\x02 \x01(\x0b\x32\x10.onnx.GraphProto\x12<\n\x16initialization_binding\x18\x03 \x03(\x0b\x32\x1c.onnx.StringStringEntryProto\x12\x34\n\x0eupdate_binding\x18\x04 \x03(\x0b\x32\x1c.onnx.StringStringEntryProto\"\xa2\x03\n\nModelProto\x12\x12\n\nir_version\x18\x01 \x01(\x03\x12.\n\x0copset_import\x18\x08 \x03(\x0b\x32\x18.onnx.OperatorSetIdProto\x12\x15\n\rproducer_name\x18\x02 \x01(\t\x12\x18\n\x10producer_version\x18\x03 \x01(\t\x12\x0e\n\x06\x64omain\x18\x04 \x01(\t\x12\x15\n\rmodel_version\x18\x05 \x01(\x03\x12\x12\n\ndoc_string\x18\x06 \x01(\t\x12\x1f\n\x05graph\x18\x07 \x01(\x0b\x32\x10.onnx.GraphProto\x12\x34\n\x0emetadata_props\x18\x0e \x03(\x0b\x32\x1c.onnx.StringStringEntryProto\x12.\n\rtraining_info\x18\x14 \x03(\x0b\x32\x17.onnx.TrainingInfoProto\x12&\n\tfunctions\x18\x19 \x03(\x0b\x32\x13.onnx.FunctionProto\x12\x35\n\rconfiguration\x18\x1a \x03(\x0b\x32\x1e.onnx.DeviceConfigurationProto\"M\n\x18\x44\x65viceConfigurationProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0bnum_devices\x18\x02 \x01(\x05\x12\x0e\n\x06\x64\x65vice\x18\x03 \x03(\t\"4\n\x16StringStringEntryProto\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"k\n\x10TensorAnnotation\x12\x13\n\x0btensor_name\x18\x01 \x01(\t\x12\x42\n\x1cquant_parameter_tensor_names\x18\x02 \x03(\x0b\x32\x1c.onnx.StringStringEntryProto\"\xd4\x03\n\nGraphProto\x12\x1d\n\x04node\x18\x01 \x03(\x0b\x32\x0f.onnx.NodeProto\x12\x0c\n\x04name\x18\x02 \x01(\t\x12&\n\x0binitializer\x18\x05 \x03(\x0b\x32\x11.onnx.TensorProto\x12\x33\n\x12sparse_initializer\x18\x0f \x03(\x0b\x32\x17.onnx.SparseTensorProto\x12\x12\n\ndoc_string\x18\n \x01(\t\x12#\n\x05input\x18\x0b \x03(\x0b\x32\x14.onnx.ValueInfoProto\x12$\n\x06output\x18\x0c \x03(\x0b\x32\x14.onnx.ValueInfoProto\x12(\n\nvalue_info\x18\r \x03(\x0b\x32\x14.onnx.ValueInfoProto\x12\x37\n\x17quantization_annotation\x18\x0e \x03(\x0b\x32\x16.onnx.TensorAnnotation\x12\x34\n\x0emetadata_props\x18\x10 \x03(\x0b\x32\x1c.onnx.StringStringEntryProtoJ\x04\x08\x03\x10\x04J\x04\x08\x04\x10\x05J\x04\x08\x06\x10\nR\nir_versionR\x10producer_versionR\x0cproducer_tagR\x06\x64omain\"\xdd\x06\n\x0bTensorProto\x12\x0c\n\x04\x64ims\x18\x01 \x03(\x03\x12\x11\n\tdata_type\x18\x02 \x01(\x05\x12*\n\x07segment\x18\x03 \x01(\x0b\x32\x19.onnx.TensorProto.Segment\x12\x16\n\nfloat_data\x18\x04 \x03(\x02\x42\x02\x10\x01\x12\x16\n\nint32_data\x18\x05 \x03(\x05\x42\x02\x10\x01\x12\x13\n\x0bstring_data\x18\x06 \x03(\x0c\x12\x16\n\nint64_data\x18\x07 \x03(\x03\x42\x02\x10\x01\x12\x0c\n\x04name\x18\x08 \x01(\t\x12\x12\n\ndoc_string\x18\x0c \x01(\t\x12\x10\n\x08raw_data\x18\t \x01(\x0c\x12\x33\n\rexternal_data\x18\r \x03(\x0b\x32\x1c.onnx.StringStringEntryProto\x12\x35\n\rdata_location\x18\x0e \x01(\x0e\x32\x1e.onnx.TensorProto.DataLocation\x12\x17\n\x0b\x64ouble_data\x18\n \x03(\x01\x42\x02\x10\x01\x12\x17\n\x0buint64_data\x18\x0b \x03(\x04\x42\x02\x10\x01\x12\x34\n\x0emetadata_props\x18\x10 \x03(\x0b\x32\x1c.onnx.StringStringEntryProto\x1a%\n\x07Segment\x12\r\n\x05\x62\x65gin\x18\x01 \x01(\x03\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x03\"\xc9\x02\n\x08\x44\x61taType\x12\r\n\tUNDEFINED\x10\x00\x12\t\n\x05\x46LOAT\x10\x01\x12\t\n\x05UINT8\x10\x02\x12\x08\n\x04INT8\x10\x03\x12\n\n\x06UINT16\x10\x04\x12\t\n\x05INT16\x10\x05\x12\t\n\x05INT32\x10\x06\x12\t\n\x05INT64\x10\x07\x12\n\n\x06STRING\x10\x08\x12\x08\n\x04\x42OOL\x10\t\x12\x0b\n\x07\x46LOAT16\x10\n\x12\n\n\x06\x44OUBLE\x10\x0b\x12\n\n\x06UINT32\x10\x0c\x12\n\n\x06UINT64\x10\r\x12\r\n\tCOMPLEX64\x10\x0e\x12\x0e\n\nCOMPLEX128\x10\x0f\x12\x0c\n\x08\x42\x46LOAT16\x10\x10\x12\x10\n\x0c\x46LOAT8E4M3FN\x10\x11\x12\x12\n\x0e\x46LOAT8E4M3FNUZ\x10\x12\x12\x0e\n\nFLOAT8E5M2\x10\x13\x12\x12\n\x0e\x46LOAT8E5M2FNUZ\x10\x14\x12\t\n\x05UINT4\x10\x15\x12\x08\n\x04INT4\x10\x16\x12\x0e\n\nFLOAT4E2M1\x10\x17\")\n\x0c\x44\x61taLocation\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x0c\n\x08\x45XTERNAL\x10\x01\"h\n\x11SparseTensorProto\x12!\n\x06values\x18\x01 \x01(\x0b\x32\x11.onnx.TensorProto\x12\"\n\x07indices\x18\x02 \x01(\x0b\x32\x11.onnx.TensorProto\x12\x0c\n\x04\x64ims\x18\x03 \x03(\x03\"\x95\x01\n\x10TensorShapeProto\x12-\n\x03\x64im\x18\x01 \x03(\x0b\x32 .onnx.TensorShapeProto.Dimension\x1aR\n\tDimension\x12\x13\n\tdim_value\x18\x01 \x01(\x03H\x00\x12\x13\n\tdim_param\x18\x02 \x01(\tH\x00\x12\x12\n\ndenotation\x18\x03 \x01(\tB\x07\n\x05value\"\xa5\x05\n\tTypeProto\x12-\n\x0btensor_type\x18\x01 \x01(\x0b\x32\x16.onnx.TypeProto.TensorH\x00\x12\x31\n\rsequence_type\x18\x04 \x01(\x0b\x32\x18.onnx.TypeProto.SequenceH\x00\x12\'\n\x08map_type\x18\x05 \x01(\x0b\x32\x13.onnx.TypeProto.MapH\x00\x12\x31\n\roptional_type\x18\t \x01(\x0b\x32\x18.onnx.TypeProto.OptionalH\x00\x12:\n\x12sparse_tensor_type\x18\x08 \x01(\x0b\x32\x1c.onnx.TypeProto.SparseTensorH\x00\x12-\n\x0bopaque_type\x18\x07 \x01(\x0b\x32\x16.onnx.TypeProto.OpaqueH\x00\x12\x12\n\ndenotation\x18\x06 \x01(\t\x1a\x42\n\x06Tensor\x12\x11\n\telem_type\x18\x01 \x01(\x05\x12%\n\x05shape\x18\x02 \x01(\x0b\x32\x16.onnx.TensorShapeProto\x1a.\n\x08Sequence\x12\"\n\telem_type\x18\x01 \x01(\x0b\x32\x0f.onnx.TypeProto\x1a<\n\x03Map\x12\x10\n\x08key_type\x18\x01 \x01(\x05\x12#\n\nvalue_type\x18\x02 \x01(\x0b\x32\x0f.onnx.TypeProto\x1a.\n\x08Optional\x12\"\n\telem_type\x18\x01 \x01(\x0b\x32\x0f.onnx.TypeProto\x1aH\n\x0cSparseTensor\x12\x11\n\telem_type\x18\x01 \x01(\x05\x12%\n\x05shape\x18\x02 \x01(\x0b\x32\x16.onnx.TensorShapeProto\x1a&\n\x06Opaque\x12\x0e\n\x06\x64omain\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\tB\x07\n\x05value\"5\n\x12OperatorSetIdProto\x12\x0e\n\x06\x64omain\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\x03\"\x86\x03\n\rFunctionProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05input\x18\x04 \x03(\t\x12\x0e\n\x06output\x18\x05 \x03(\t\x12\x11\n\tattribute\x18\x06 \x03(\t\x12-\n\x0f\x61ttribute_proto\x18\x0b \x03(\x0b\x32\x14.onnx.AttributeProto\x12\x1d\n\x04node\x18\x07 \x03(\x0b\x32\x0f.onnx.NodeProto\x12\x12\n\ndoc_string\x18\x08 \x01(\t\x12.\n\x0copset_import\x18\t \x03(\x0b\x32\x18.onnx.OperatorSetIdProto\x12\x0e\n\x06\x64omain\x18\n \x01(\t\x12\x10\n\x08overload\x18\r \x01(\t\x12(\n\nvalue_info\x18\x0c \x03(\x0b\x32\x14.onnx.ValueInfoProto\x12\x34\n\x0emetadata_props\x18\x0e \x03(\x0b\x32\x1c.onnx.StringStringEntryProtoJ\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04R\rsince_versionR\x06status*\xb1\x02\n\x07Version\x12\x12\n\x0e_START_VERSION\x10\x00\x12\x19\n\x15IR_VERSION_2017_10_10\x10\x01\x12\x19\n\x15IR_VERSION_2017_10_30\x10\x02\x12\x18\n\x14IR_VERSION_2017_11_3\x10\x03\x12\x18\n\x14IR_VERSION_2019_1_22\x10\x04\x12\x18\n\x14IR_VERSION_2019_3_18\x10\x05\x12\x18\n\x14IR_VERSION_2019_9_19\x10\x06\x12\x17\n\x13IR_VERSION_2020_5_8\x10\x07\x12\x18\n\x14IR_VERSION_2021_7_30\x10\x08\x12\x17\n\x13IR_VERSION_2023_5_5\x10\t\x12\x18\n\x14IR_VERSION_2024_3_25\x10\n\x12\x0e\n\nIR_VERSION\x10\x0b*.\n\x0eOperatorStatus\x12\x10\n\x0c\x45XPERIMENTAL\x10\x00\x12\n\n\x06STABLE\x10\x01\x42\x02H\x03')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'onnx.onnx_ml_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'H\003'
  _globals['_TENSORPROTO'].fields_by_name['float_data']._options = None
  _globals['_TENSORPROTO'].fields_by_name['float_data']._serialized_options = b'\020\001'
  _globals['_TENSORPROTO'].fields_by_name['int32_data']._options = None
  _globals['_TENSORPROTO'].fields_by_name['int32_data']._serialized_options = b'\020\001'
  _globals['_TENSORPROTO'].fields_by_name['int64_data']._options = None
  _globals['_TENSORPROTO'].fields_by_name['int64_data']._serialized_options = b'\020\001'
  _globals['_TENSORPROTO'].fields_by_name['double_data']._options = None
  _globals['_TENSORPROTO'].fields_by_name['double_data']._serialized_options = b'\020\001'
  _globals['_TENSORPROTO'].fields_by_name['uint64_data']._options = None
  _globals['_TENSORPROTO'].fields_by_name['uint64_data']._serialized_options = b'\020\001'
  _globals['_VERSION']._serialized_start=5324
  _globals['_VERSION']._serialized_end=5629
  _globals['_OPERATORSTATUS']._serialized_start=5631
  _globals['_OPERATORSTATUS']._serialized_end=5677
  _globals['_ATTRIBUTEPROTO']._serialized_start=29
  _globals['_ATTRIBUTEPROTO']._serialized_end=760
  _globals['_ATTRIBUTEPROTO_ATTRIBUTETYPE']._serialized_start=528
  _globals['_ATTRIBUTEPROTO_ATTRIBUTETYPE']._serialized_end=745
  _globals['_VALUEINFOPROTO']._serialized_start=763
  _globals['_VALUEINFOPROTO']._serialized_end=898
  _globals['_NODEPROTO']._serialized_start=901
  _globals['_NODEPROTO']._serialized_end=1190
  _globals['_INTINTLISTENTRYPROTO']._serialized_start=1192
  _globals['_INTINTLISTENTRYPROTO']._serialized_end=1242
  _globals['_NODEDEVICECONFIGURATIONPROTO']._serialized_start=1245
  _globals['_NODEDEVICECONFIGURATIONPROTO']._serialized_end=1373
  _globals['_SHARDINGSPECPROTO']._serialized_start=1376
  _globals['_SHARDINGSPECPROTO']._serialized_end=1539
  _globals['_SHARDEDDIMPROTO']._serialized_start=1541
  _globals['_SHARDEDDIMPROTO']._serialized_end=1626
  _globals['_SIMPLESHARDEDDIMPROTO']._serialized_start=1628
  _globals['_SIMPLESHARDEDDIMPROTO']._serialized_end=1720
  _globals['_TRAININGINFOPROTO']._serialized_start=1723
  _globals['_TRAININGINFOPROTO']._serialized_end=1937
  _globals['_MODELPROTO']._serialized_start=1940
  _globals['_MODELPROTO']._serialized_end=2358
  _globals['_DEVICECONFIGURATIONPROTO']._serialized_start=2360
  _globals['_DEVICECONFIGURATIONPROTO']._serialized_end=2437
  _globals['_STRINGSTRINGENTRYPROTO']._serialized_start=2439
  _globals['_STRINGSTRINGENTRYPROTO']._serialized_end=2491
  _globals['_TENSORANNOTATION']._serialized_start=2493
  _globals['_TENSORANNOTATION']._serialized_end=2600
  _globals['_GRAPHPROTO']._serialized_start=2603
  _globals['_GRAPHPROTO']._serialized_end=3071
  _globals['_TENSORPROTO']._serialized_start=3074
  _globals['_TENSORPROTO']._serialized_end=3935
  _globals['_TENSORPROTO_SEGMENT']._serialized_start=3523
  _globals['_TENSORPROTO_SEGMENT']._serialized_end=3560
  _globals['_TENSORPROTO_DATATYPE']._serialized_start=3563
  _globals['_TENSORPROTO_DATATYPE']._serialized_end=3892
  _globals['_TENSORPROTO_DATALOCATION']._serialized_start=3894
  _globals['_TENSORPROTO_DATALOCATION']._serialized_end=3935
  _globals['_SPARSETENSORPROTO']._serialized_start=3937
  _globals['_SPARSETENSORPROTO']._serialized_end=4041
  _globals['_TENSORSHAPEPROTO']._serialized_start=4044
  _globals['_TENSORSHAPEPROTO']._serialized_end=4193
  _globals['_TENSORSHAPEPROTO_DIMENSION']._serialized_start=4111
  _globals['_TENSORSHAPEPROTO_DIMENSION']._serialized_end=4193
  _globals['_TYPEPROTO']._serialized_start=4196
  _globals['_TYPEPROTO']._serialized_end=4873
  _globals['_TYPEPROTO_TENSOR']._serialized_start=4526
  _globals['_TYPEPROTO_TENSOR']._serialized_end=4592
  _globals['_TYPEPROTO_SEQUENCE']._serialized_start=4594
  _globals['_TYPEPROTO_SEQUENCE']._serialized_end=4640
  _globals['_TYPEPROTO_MAP']._serialized_start=4642
  _globals['_TYPEPROTO_MAP']._serialized_end=4702
  _globals['_TYPEPROTO_OPTIONAL']._serialized_start=4704
  _globals['_TYPEPROTO_OPTIONAL']._serialized_end=4750
  _globals['_TYPEPROTO_SPARSETENSOR']._serialized_start=4752
  _globals['_TYPEPROTO_SPARSETENSOR']._serialized_end=4824
  _globals['_TYPEPROTO_OPAQUE']._serialized_start=4826
  _globals['_TYPEPROTO_OPAQUE']._serialized_end=4864
  _globals['_OPERATORSETIDPROTO']._serialized_start=4875
  _globals['_OPERATORSETIDPROTO']._serialized_end=4928
  _globals['_FUNCTIONPROTO']._serialized_start=4931
  _globals['_FUNCTIONPROTO']._serialized_end=5321
# @@protoc_insertion_point(module_scope)
