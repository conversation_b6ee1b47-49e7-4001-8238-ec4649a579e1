#!/usr/bin/env python3
"""
Manual dependency installation script for YOLO-NAS Barcode Detection

This script handles the installation of dependencies with proper version compatibility
for Python 3.11 and resolves the super-gradients compatibility issues.

Usage:
    python install_dependencies.py
    python install_dependencies.py --cpu-only
"""

import subprocess
import sys
import argparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_command(command):
    """Run a command and return success status."""
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True, shell=True)
        logger.info(f"✅ Successfully installed: {command}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install: {command}")
        logger.error(f"Error: {e.stderr}")
        return False


def install_pytorch(cpu_only=False):
    """Install PyTorch with proper CUDA support."""
    logger.info("Installing PyTorch...")
    
    if cpu_only:
        command = "uv pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    else:
        # Install CUDA version (default)
        command = "uv pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121"
    
    return run_command(command)


def install_super_gradients():
    """Install super-gradients with compatibility fixes."""
    logger.info("Installing super-gradients...")
    
    # Try the latest version first
    if run_command("uv pip install super-gradients"):
        return True
    
    # If that fails, try a specific compatible version
    logger.info("Trying alternative super-gradients version...")
    if run_command("uv pip install super-gradients==3.7.1"):
        return True
    
    # Last resort: install from source
    logger.info("Trying to install super-gradients from GitHub...")
    return run_command("uv pip install git+https://github.com/Deci-AI/super-gradients.git")


def install_core_dependencies():
    """Install core computer vision and ML dependencies."""
    logger.info("Installing core dependencies...")
    
    dependencies = [
        "numpy>=1.24.0",
        "pillow>=10.0.0",
        "opencv-python>=4.8.0",
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0",
        "pandas>=2.0.0",
        "scikit-learn>=1.3.0",
        "tqdm>=4.65.0",
        "pyyaml>=6.0",
        "tensorboard>=2.13.0",
    ]
    
    for dep in dependencies:
        if not run_command(f"uv pip install {dep}"):
            return False
    
    return True


def install_cv_dependencies():
    """Install computer vision specific dependencies."""
    logger.info("Installing computer vision dependencies...")
    
    dependencies = [
        "albumentations>=1.3.0",
        "supervision>=0.16.0",
        "roboflow>=1.1.0",
    ]
    
    for dep in dependencies:
        if not run_command(f"uv pip install {dep}"):
            logger.warning(f"Failed to install {dep}, continuing...")
    
    return True


def install_coco_tools():
    """Install pycocotools with proper compilation."""
    logger.info("Installing pycocotools...")
    
    # Try pre-compiled version first
    if run_command("uv pip install pycocotools"):
        return True
    
    # If that fails, try alternative
    logger.info("Trying alternative pycocotools installation...")
    return run_command("uv pip install pycocotools-windows") or run_command("uv pip install 'git+https://github.com/cocodataset/cocoapi.git#subdirectory=PythonAPI'")


def install_jupyter():
    """Install Jupyter and related packages."""
    logger.info("Installing Jupyter...")
    
    dependencies = [
        "jupyter>=1.0.0",
        "ipykernel>=6.25.0",
        "notebook>=6.5.0",
    ]
    
    for dep in dependencies:
        if not run_command(f"uv pip install {dep}"):
            logger.warning(f"Failed to install {dep}, continuing...")
    
    return True


def verify_installation():
    """Verify that key packages are installed correctly."""
    logger.info("Verifying installation...")
    
    test_imports = [
        ("torch", "PyTorch"),
        ("torchvision", "TorchVision"),
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("PIL", "Pillow"),
        ("matplotlib", "Matplotlib"),
        ("pandas", "Pandas"),
        ("sklearn", "Scikit-learn"),
        ("yaml", "PyYAML"),
        ("tqdm", "TQDM"),
    ]
    
    failed_imports = []
    
    for module, name in test_imports:
        try:
            __import__(module)
            logger.info(f"✅ {name} imported successfully")
        except ImportError:
            logger.error(f"❌ Failed to import {name}")
            failed_imports.append(name)
    
    # Test super-gradients separately
    try:
        import super_gradients
        logger.info(f"✅ Super-gradients imported successfully (version: {super_gradients.__version__})")
    except ImportError:
        logger.error("❌ Failed to import super-gradients")
        failed_imports.append("super-gradients")
    
    if failed_imports:
        logger.error(f"Failed to import: {', '.join(failed_imports)}")
        return False
    else:
        logger.info("🎉 All packages imported successfully!")
        return True


def main():
    parser = argparse.ArgumentParser(description='Install YOLO-NAS dependencies')
    parser.add_argument('--cpu-only', action='store_true',
                        help='Install CPU-only version of PyTorch')
    
    args = parser.parse_args()
    
    logger.info("Starting dependency installation...")
    logger.info("=" * 60)
    
    # Installation steps
    steps = [
        ("PyTorch", lambda: install_pytorch(args.cpu_only)),
        ("Core dependencies", install_core_dependencies),
        ("Computer vision dependencies", install_cv_dependencies),
        ("COCO tools", install_coco_tools),
        ("Super-gradients", install_super_gradients),
        ("Jupyter", install_jupyter),
    ]
    
    failed_steps = []
    
    for step_name, step_function in steps:
        logger.info(f"\n📦 Installing {step_name}...")
        try:
            if step_function():
                logger.info(f"✅ {step_name} installed successfully")
            else:
                logger.error(f"❌ {step_name} installation failed")
                failed_steps.append(step_name)
        except Exception as e:
            logger.error(f"❌ {step_name} installation failed with exception: {e}")
            failed_steps.append(step_name)
    
    # Verification
    logger.info("\n🔍 Verifying installation...")
    verification_success = verify_installation()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("INSTALLATION SUMMARY")
    logger.info("=" * 60)
    
    if not failed_steps and verification_success:
        logger.info("🎉 All dependencies installed successfully!")
        logger.info("\nNext steps:")
        logger.info("1. Convert your dataset: python scripts/utils/convert_coco_to_yolo.py")
        logger.info("2. Verify CUDA: python scripts/utils/check_cuda.py")
        logger.info("3. Start training: python scripts/train.py")
    else:
        if failed_steps:
            logger.error(f"❌ Failed to install: {', '.join(failed_steps)}")
        if not verification_success:
            logger.error("❌ Package verification failed")
        
        logger.info("\nTroubleshooting tips:")
        logger.info("1. Make sure you're in the virtual environment")
        logger.info("2. Try installing failed packages individually")
        logger.info("3. Check your Python version (3.8-3.11 supported)")
        logger.info("4. For Windows: Install Visual Studio Build Tools if needed")


if __name__ == "__main__":
    main()
