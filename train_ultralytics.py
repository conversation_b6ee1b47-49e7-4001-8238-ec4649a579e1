#!/usr/bin/env python3
"""
Ultralytics YOLO Training Script for Barcode Detection

This script trains YOLOv8 models on your COCO format barcode dataset.
Much simpler and more reliable than super-gradients!

Usage:
    python train_ultralytics.py
"""

import os
from pathlib import Path
from ultralytics import YOLO
import yaml

def create_ultralytics_config():
    """Create Ultralytics dataset configuration from COCO format."""
    
    # Your COCO dataset structure
    dataset_path = Path("Barcodes.v5i.coco")
    
    # Create Ultralytics format config
    config = {
        'path': str(dataset_path.absolute()),
        'train': 'train',
        'val': 'valid', 
        'test': 'test',
        'nc': 3,  # number of classes
        'names': ['Barcodes', 'Barcode', 'QR Code']  # class names from your dataset
    }
    
    # Save config file
    config_path = 'barcode_dataset.yaml'
    with open(config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    print(f"✅ Created dataset config: {config_path}")
    return config_path

def train_yolo_model():
    """Train YOLOv8 model on barcode dataset."""
    
    print("🚀 Starting YOLOv8 training for barcode detection...")
    
    # Create dataset config
    config_path = create_ultralytics_config()
    
    # Load YOLOv8 model (you can choose different sizes)
    # yolov8n.pt - nano (fastest, smallest)
    # yolov8s.pt - small 
    # yolov8m.pt - medium
    # yolov8l.pt - large (best accuracy)
    model = YOLO('yolov8s.pt')  # Good balance of speed and accuracy
    
    # Train the model
    results = model.train(
        data=config_path,
        epochs=100,
        imgsz=640,
        batch=16,  # Adjust based on your GPU memory
        device=0,  # Use GPU (0) or 'cpu'
        project='runs/train',
        name='barcode_detection',
        save=True,
        save_period=10,  # Save checkpoint every 10 epochs
        patience=15,  # Early stopping patience
        
        # Data augmentation
        hsv_h=0.015,
        hsv_s=0.7,
        hsv_v=0.4,
        degrees=0.0,
        translate=0.1,
        scale=0.5,
        shear=0.0,
        perspective=0.0,
        flipud=0.0,
        fliplr=0.5,
        mosaic=1.0,
        mixup=0.0,
        
        # Optimization
        optimizer='AdamW',
        lr0=0.01,
        lrf=0.01,
        momentum=0.937,
        weight_decay=0.0005,
        warmup_epochs=3.0,
        warmup_momentum=0.8,
        warmup_bias_lr=0.1,
        
        # Validation
        val=True,
        plots=True,
        verbose=True
    )
    
    print("✅ Training completed!")
    print(f"📊 Results saved to: runs/train/barcode_detection")
    print(f"🏆 Best model: runs/train/barcode_detection/weights/best.pt")
    
    return results

def test_model():
    """Test the trained model."""
    
    model_path = "runs/train/barcode_detection/weights/best.pt"
    
    if os.path.exists(model_path):
        print("🧪 Testing trained model...")
        
        # Load trained model
        model = YOLO(model_path)
        
        # Validate on test set
        results = model.val(data='barcode_dataset.yaml', split='test')
        
        print("✅ Model testing completed!")
        print(f"📊 mAP50: {results.box.map50:.3f}")
        print(f"📊 mAP50-95: {results.box.map:.3f}")
        
        return results
    else:
        print("❌ No trained model found. Train first!")
        return None

def run_inference_example():
    """Run inference on sample images."""
    
    model_path = "runs/train/barcode_detection/weights/best.pt"
    
    if os.path.exists(model_path):
        print("🔍 Running inference example...")
        
        # Load trained model
        model = YOLO(model_path)
        
        # Run inference on test images
        test_images = Path("Barcodes.v5i.coco/test")
        if test_images.exists():
            results = model.predict(
                source=str(test_images),
                save=True,
                save_txt=True,
                conf=0.5,
                project='runs/detect',
                name='barcode_inference'
            )
            
            print("✅ Inference completed!")
            print(f"📁 Results saved to: runs/detect/barcode_inference")
        else:
            print("❌ Test images directory not found")
    else:
        print("❌ No trained model found. Train first!")

def main():
    """Main training pipeline."""
    
    print("🎯 YOLOv8 Barcode Detection Training")
    print("=" * 50)
    
    # Check CUDA
    import torch
    if torch.cuda.is_available():
        print(f"✅ CUDA available: {torch.cuda.get_device_name(0)}")
    else:
        print("⚠️  CUDA not available, using CPU (slower)")
    
    # Check dataset
    dataset_path = Path("Barcodes.v5i.coco")
    if not dataset_path.exists():
        print("❌ Dataset not found: Barcodes.v5i.coco")
        print("Please ensure your COCO dataset is in the current directory")
        return
    
    print(f"✅ Dataset found: {dataset_path}")
    
    # Train model
    train_results = train_yolo_model()
    
    # Test model
    test_results = test_model()
    
    # Run inference example
    run_inference_example()
    
    print("\n🎉 Complete pipeline finished!")
    print("\nNext steps:")
    print("1. Check training plots: runs/train/barcode_detection/")
    print("2. Use best model: runs/train/barcode_detection/weights/best.pt")
    print("3. Run inference: yolo predict model=runs/train/barcode_detection/weights/best.pt source=your_image.jpg")

if __name__ == "__main__":
    main()
