{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# YOLO-NAS Barcode Detection - Getting Started\n", "\n", "This notebook provides a quick start guide for training and using YOLO-NAS models for barcode detection."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup\n", "\n", "First, let's verify our environment is set up correctly:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check CUDA setup\n", "!python ../scripts/utils/check_cuda.py"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Dataset Conversion\n", "\n", "Convert COCO format dataset to YOLO format:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert dataset\n", "!python ../scripts/utils/convert_coco_to_yolo.py --input ../Barcodes.v5i.coco --output ../data/processed"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Training\n", "\n", "Start training a YOLO-NAS model:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start training\n", "!python ../scripts/train.py --config ../configs/training/yolo_nas_default.yaml"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Evaluation\n", "\n", "Evaluate the trained model:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluate model\n", "!python ../scripts/evaluate.py --model ../models/best/model.pth --data ../data/processed/test"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Inference\n", "\n", "Run inference on new images:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run inference\n", "!python ../scripts/inference.py --model ../models/best/model.pth --input ../data/processed/test/images --output ../results/inference"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}