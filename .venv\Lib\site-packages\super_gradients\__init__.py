__version__ = "3.7.1"

from super_gradients.common import init_trainer, is_distributed, object_names
from super_gradients.training import losses, utils, datasets_utils, DataAugmentation, Trainer, KDTrainer, QATTrainer
from super_gradients.common.registry.registry import ARCHITECTURES
from super_gradients.sanity_check import env_sanity_check
from super_gradients.training.utils.distributed_training_utils import setup_device
from super_gradients.training.pre_launch_callbacks import AutoTrainBatchSizeSelectionCallback, QATRecipeModificationCallback

__all__ = [
    "ARCHITECTURES",
    "losses",
    "utils",
    "datasets_utils",
    "DataAugmentation",
    "Trainer",
    "KDTrainer",
    "QATTrainer",
    "object_names",
    "init_trainer",
    "is_distributed",
    "env_sanity_check",
    "setup_device",
    "QATRecipeModificationCallback",
    "AutoTrainBatchSizeSelectionCallback",
]

env_sanity_check()
